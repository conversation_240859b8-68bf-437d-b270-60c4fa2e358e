"""
网站流程处理器框架
提供通用的网页交互框架和网站特定的流程实现
"""

from abc import ABC, abstractmethod
from typing import Optional
from playwright.async_api import Page

from ..core.logger import LoggerMixin
from ..core.config_manager import SiteConfig


class BaseWebWorkflow(LoggerMixin, ABC):
    """
    通用网页交互流程框架基类
    定义所有网站流程处理器必须实现的核心接口
    提供通用的网页操作方法
    """
    
    def __init__(self, page: Page, config: SiteConfig):
        """
        初始化流程处理器
        
        Args:
            page: Playwright页面对象
            config: 网站配置
        """
        self.page = page
        self.config = config
        self.is_logged_in = None
    
    @abstractmethod
    async def check_login_status(self) -> Optional[bool]:
        """
        检查登录状态
        
        Returns:
            True: 已登录, False: 未登录, None: 无法确定
        """
        pass
    
    @abstractmethod
    async def handle_login_required(self) -> bool:
        """
        处理需要登录的情况
        
        Returns:
            是否成功处理
        """
        pass
    
    @abstractmethod
    async def prepare_for_message_sending(self) -> bool:
        """
        发送消息前的准备工作
        
        Returns:
            是否准备成功
        """
        pass
    
    @abstractmethod
    async def send_message(self, message: str) -> bool:
        """
        发送消息
        
        Args:
            message: 要发送的消息
        
        Returns:
            是否发送成功
        """
        pass
    
    async def wait_for_element(self, selector: str, timeout: int = 10000, state: str = 'visible') -> bool:
        """
        等待元素出现
        
        Args:
            selector: 元素选择器
            timeout: 超时时间（毫秒）
            state: 元素状态
        
        Returns:
            元素是否出现
        """
        try:
            await self.page.wait_for_selector(selector, timeout=timeout, state=state)
            return True
        except Exception as e:
            self.logger.debug(f"等待元素失败 {selector}: {e}")
            return False
    
    async def click_element(self, selector: str, timeout: int = 5000) -> bool:
        """
        点击元素
        
        Args:
            selector: 元素选择器
            timeout: 超时时间（毫秒）
        
        Returns:
            是否点击成功
        """
        try:
            element = await self.page.wait_for_selector(selector, timeout=timeout)
            if element:
                await element.click()
                return True
            return False
        except Exception as e:
            self.logger.debug(f"点击元素失败 {selector}: {e}")
            return False
    
    async def get_element_text(self, selector: str, timeout: int = 5000) -> Optional[str]:
        """
        获取元素文本
        
        Args:
            selector: 元素选择器
            timeout: 超时时间（毫秒）
        
        Returns:
            元素文本或None
        """
        try:
            element = await self.page.wait_for_selector(selector, timeout=timeout)
            if element:
                return await element.inner_text()
            return None
        except Exception as e:
            self.logger.debug(f"获取元素文本失败 {selector}: {e}")
            return None





def create_workflow(site_id: str, page: Page, config: SiteConfig) -> BaseWebWorkflow:
    """
    工厂函数：根据网站ID创建对应的流程处理器

    Args:
        site_id: 网站标识符
        page: Playwright页面对象
        config: 网站配置

    Returns:
        对应的流程处理器实例
    """
    if site_id == 'doubao':
        from .workflows.doubao_workflow import DoubaoWorkflow
        return DoubaoWorkflow(page, config)
    else:
        # 默认返回基础流程处理器（需要实现一个默认的）
        raise NotImplementedError(f"网站 {site_id} 的流程处理器尚未实现")
