# 网页互动程序 - 第一版简化版

这是一个基于Python的网页自动化交互程序，专注于核心功能的实现。

## 功能特性

### 第一版核心功能
- ✅ **PyQt5 GUI界面** - 简洁的用户界面
- ✅ **Playwright网页引擎** - 网页自动化能力
- ✅ **配置管理系统** - 多网站配置支持
- ✅ **消息监控** - 实时抓取网页消息
- ✅ **基础反爬虫** - 集成playwright-stealth
- ✅ **单一连接控制** - 一次只能连接一个网页
- ✅ **浏览器管理** - 支持打开和关闭浏览器连接

## 快速开始

### 1. 安装依赖

```bash
# 安装PyQt5（推荐使用conda）
conda install pyqt

# 安装其他依赖
pip install qasync playwright playwright-stealth
```

### 2. 安装Playwright浏览器

```bash
playwright install chromium
```

### 3. 启动程序

```bash
python main.py
```

## 使用说明

### 界面功能

1. **网站选择**：从下拉菜单选择要连接的网站
2. **打开网页**：点击"打开网页"按钮连接到选中的网站
3. **关闭浏览器**：点击"关闭浏览器"按钮断开当前连接
4. **消息发送**：在输入框中输入消息并发送

### 操作流程

1. 启动程序后，选择要连接的网站
2. 点击"打开网页"按钮，程序会：
   - 启动浏览器
   - 导航到目标网站
   - 开始监控消息
   - 自动保存页面HTML
3. 连接成功后可以：
   - 在输入框中输入消息并发送
   - 查看抓取到的消息
   - 页面HTML会自动保存到 `saved_pages/` 目录
4. 使用完毕后点击"关闭浏览器"断开连接

### 安全特性

- **单一连接**：同时只能连接一个网页，避免资源冲突
- **状态管理**：按钮状态会根据连接状态自动调整
- **错误处理**：连接失败时会自动恢复按钮状态

## 项目结构

```
AI-server/
├── main.py                 # 主入口文件
├── run.py                  # 快速启动脚本
├── requirements.txt        # 依赖列表
├── src/                    # 源代码目录
│   ├── core/              # 核心模块
│   │   ├── config_manager.py  # 配置管理器
│   │   └── logger.py          # 日志管理器
│   ├── engine/            # 网页引擎
│   │   └── web_engine.py      # 网页交互引擎
│   ├── gui/               # GUI界面
│   │   └── main_window.py     # 主窗口
│   └── api/               # API接口
│       └── server.py          # FastAPI服务器
├── configs/               # 配置文件目录
│   ├── default/          # 默认配置
│   └── example_site/     # 示例网站配置
├── logs/                 # 日志文件目录
├── saved_pages/          # 保存的HTML文件
└── storage/              # 登录状态存储
```

## 配置网站

### 1. 创建网站配置

在 `configs/` 目录下创建新的网站配置文件夹，例如 `configs/my_site/`：

```json
{
  "site_id": "my_site",
  "site_name": "我的网站",
  "target_url": "https://example.com/chat",
  "html_save_path": "saved_pages/my_site.html",
  "selectors": {
    "message_box_selector": "div.chat-messages",
    "input_box_selector": "input#message-input",
    "send_button_selector": "button#send-btn",
    "login_status": {
      "logged_in_indicator": ".user-avatar",
      "logged_out_indicator": ".login-button",
      "check_interval_seconds": 60,
      "auto_restore_on_logout": true
    }
  },
  "storage": {
    "cookies_file": "cookies.json",
    "session_storage_file": "session.json",
    "auto_save_interval_minutes": 30
  },
  "anti_crawler": {
    "request_delay_ms": [1000, 3000]
  }
}
```

### 2. 设置选择器

使用浏览器开发者工具（F12）找到正确的CSS选择器：

1. **消息显示区域** (`message_box_selector`) - 显示聊天消息的容器
2. **输入框** (`input_box_selector`) - 消息输入框
3. **发送按钮** (`send_button_selector`) - 发送消息的按钮
4. **登录状态指示器** - 用于检测登录状态的元素

## API 接口

程序提供完整的RESTful API接口：

### 基础功能
- `GET /api/sites` - 获取所有网站配置
- `POST /api/site/select` - 切换网站配置
- `POST /api/navigate` - 导航到网站
- `POST /api/send` - 发送消息
- `GET /api/messages` - 获取消息列表

### 登录管理
- `GET /api/login/status` - 获取登录状态
- `POST /api/login/save` - 保存登录状态
- `POST /api/login/restore` - 恢复登录状态

### 工具功能
- `POST /api/html/save` - 保存页面HTML

API文档在程序启动后可通过 `http://localhost:8000/docs` 访问。

## 使用说明

### GUI模式使用

1. 启动程序后，从下拉菜单选择网站配置
2. 点击"跳转"按钮导航到网站
3. 在输入框中输入消息，点击"发送"
4. 抓取到的消息会显示在消息区域
5. 使用"保存HTML"按钮保存当前页面用于调试

### API模式使用

```bash
# 获取网站列表
curl http://localhost:8000/api/sites

# 选择网站
curl -X POST http://localhost:8000/api/site/select \
  -H "Content-Type: application/json" \
  -d '{"site_id": "default"}'

# 导航到网站
curl -X POST http://localhost:8000/api/navigate \
  -H "Content-Type: application/json" \
  -d '{"url": "https://httpbin.org/html"}'

# 发送消息
curl -X POST http://localhost:8000/api/send \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello World"}'

# 获取消息
curl http://localhost:8000/api/messages
```

## 开发说明

### 技术栈
- **Python 3.8+** - 主要编程语言
- **PyQt6** - GUI框架
- **Playwright** - 网页自动化
- **FastAPI** - API框架
- **qasync** - 异步事件循环集成

### 架构设计
- **模块化设计** - 核心功能分离，便于维护和扩展
- **异步架构** - 全面支持异步操作，提高性能
- **配置驱动** - 通过配置文件管理不同网站
- **插件化** - 为后续功能扩展预留接口

## 故障排除

### 常见问题

1. **Playwright浏览器未安装**
   ```bash
   python run.py install
   ```

2. **选择器无效**
   - 使用浏览器开发者工具重新获取选择器
   - 确保选择器在目标网站上存在

3. **程序无法启动**
   - 检查Python版本（需要3.8+）
   - 确保所有依赖已正确安装

4. **网页加载失败**
   - 检查网络连接
   - 确认目标URL可访问
   - 查看日志文件获取详细错误信息

### 日志查看

程序运行时会在 `logs/` 目录生成日志文件，包含详细的运行信息和错误信息。

## 后续计划

### 第二阶段功能
- 消息去重和时间戳
- 代理IP池支持
- User-Agent轮换
- 增强的反爬虫机制
- 多账号登录管理

### 第三阶段功能
- 实时消息监听（MutationObserver）
- 自动登录流程
- 验证码处理
- 可执行文件打包

## 许可证

本项目仅供学习和研究使用。
