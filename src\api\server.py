"""
API服务器模块 - 第一版暂时禁用
专注于GUI功能的实现
"""

from ..core.logger import LoggerMixin


class APIServer(LoggerMixin):
    """简化的API服务器类 - 第一版暂时不实现"""

    def __init__(self, config_manager):
        """初始化API服务器"""
        self.config_manager = config_manager
        self.logger.info("API服务器已禁用（第一版专注GUI功能）")

    async def start(self):
        """启动API服务器 - 暂时不实现"""
        self.logger.info("API功能将在后续版本中实现")
        pass

    async def stop(self):
        """停止API服务器 - 暂时不实现"""
        pass



