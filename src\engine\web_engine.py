"""
网页交互引擎
基于Playwright实现网页自动化操作
"""

import asyncio
import random
from pathlib import Path
from typing import Optional, List, Dict, Any
from playwright.async_api import async_playwright, <PERSON><PERSON>er, BrowserContext, Page

from ..core.logger import LoggerMixin
from ..core.config_manager import SiteConfig
from .site_workflow import create_workflow, BaseWebWorkflow


class WebEngine(LoggerMixin):
    """网页交互引擎"""

    def __init__(self):
        """初始化网页引擎"""
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.current_config: Optional[SiteConfig] = None
        self.workflow: Optional[BaseWebWorkflow] = None

        # 消息存储
        self._messages: List[Dict[str, Any]] = []
        self._last_message_count = 0

        # 监控任务
        self._monitoring_task: Optional[asyncio.Task] = None
        self._login_monitoring_task: Optional[asyncio.Task] = None

        # 回调函数
        self._login_required_callback = None
        self._login_status_changed_callback = None

        # 状态跟踪
        self._last_login_status = None
        self._last_login_prompt_time = 0  # 上次登录提醒时间
    
    async def start(self, headless: bool = False):
        """
        启动浏览器引擎
        
        Args:
            headless: 是否以无头模式运行
        """
        try:
            self.playwright = await async_playwright().start()
            
            # 启动浏览器
            self.browser = await self.playwright.chromium.launch(
                headless=headless,
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            self.logger.info(f"浏览器引擎启动成功，无头模式: {headless}")
            
        except Exception as e:
            self.logger.error(f"启动浏览器引擎失败: {e}")
            raise
    
    async def stop(self):
        """停止浏览器引擎"""
        try:
            self.logger.info("正在停止浏览器引擎...")

            # 在关闭前保存登录状态
            if self.context and self.current_config and self._last_login_status is True:
                self.logger.info("程序关闭前保存登录状态...")
                await self._save_login_state()

            # 停止监控任务
            if self._monitoring_task:
                self.logger.debug("正在取消消息监控任务...")
                self._monitoring_task.cancel()
                try:
                    await self._monitoring_task
                except asyncio.CancelledError:
                    pass
                self._monitoring_task = None

            if self._login_monitoring_task:
                self.logger.debug("正在取消登录监控任务...")
                self._login_monitoring_task.cancel()
                try:
                    await self._login_monitoring_task
                except asyncio.CancelledError:
                    pass
                self._login_monitoring_task = None
            
            # 关闭页面
            if self.page:
                self.logger.debug("正在关闭页面...")
                await self.page.close()
                self.page = None
            
            # 关闭上下文
            if self.context:
                self.logger.debug("正在关闭浏览器上下文...")
                await self.context.close()
                self.context = None
            
            # 关闭浏览器
            if self.browser:
                self.logger.debug("正在关闭浏览器...")
                await self.browser.close()
                self.browser = None
            
            # 停止playwright
            if self.playwright:
                self.logger.debug("正在停止Playwright...")
                await self.playwright.stop()
                self.playwright = None
            
            self.logger.info("浏览器引擎已完全停止")
            
        except Exception as e:
            self.logger.error(f"停止浏览器引擎时出错: {e}")

    def set_login_required_callback(self, callback):
        """
        设置登录需求回调函数

        Args:
            callback: 当检测到需要登录时调用的函数
        """
        self._login_required_callback = callback

    def set_login_status_changed_callback(self, callback):
        """
        设置登录状态变化回调函数

        Args:
            callback: 当登录状态发生变化时调用的函数，参数为新的登录状态
        """
        self._login_status_changed_callback = callback
    
    async def load_site_config(self, config: SiteConfig, site_id: str = None):
        """
        加载网站配置并创建新的浏览器上下文

        Args:
            config: 网站配置
            site_id: 网站标识符，用于创建对应的流程处理器
        """
        try:
            self.current_config = config

            # 如果已有上下文，先关闭
            if self.context:
                await self.context.close()

            # 创建新的浏览器上下文
            context_options = {
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }

            # 尝试加载已保存的登录状态
            site_name = config.site_name.lower()
            storage_dir = Path("storage") / site_name
            storage_file = storage_dir / config.storage.get('cookies_file', 'cookies.json')

            if storage_file.exists():
                context_options['storage_state'] = str(storage_file)
                file_size = storage_file.stat().st_size
                self.logger.info(f"✅ 加载已保存的登录状态: {storage_file} ({file_size} 字节)")
            else:
                self.logger.info(f"📁 登录状态文件不存在: {storage_file}")

            self.context = await self.browser.new_context(**context_options)

            # 应用stealth模式
            await self._apply_stealth_mode()

            # 创建新页面
            self.page = await self.context.new_page()

            # 创建网站特定的流程处理器
            if site_id:
                try:
                    self.workflow = create_workflow(site_id, self.page, config)
                    self.logger.info(f"成功创建 {site_id} 流程处理器")
                except NotImplementedError as e:
                    self.logger.warning(f"流程处理器创建失败: {e}")
                    self.workflow = None

            self.logger.info(f"成功加载网站配置: {config.site_name}")

        except Exception as e:
            self.logger.error(f"加载网站配置失败: {e}")
            raise
    
    async def _apply_stealth_mode(self):
        """应用反爬虫隐身模式"""
        try:
            # 注入stealth脚本
            await self.context.add_init_script("""
                // 移除webdriver属性
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                
                // 修改plugins
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });
                
                // 修改languages
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['zh-CN', 'zh', 'en'],
                });
            """)
            
            self.logger.debug("已应用反爬虫隐身模式")
            
        except Exception as e:
            self.logger.error(f"应用隐身模式失败: {e}")
    
    async def navigate_to_site(self) -> bool:
        """
        导航到配置的网站URL，包含完整的页面加载等待和登录状态检查流程

        Returns:
            导航是否成功
        """
        if not self.page or not self.current_config:
            self.logger.error("页面或配置未初始化")
            return False

        try:
            self.logger.info(f"开始导航到: {self.current_config.target_url}")

            # 第一步：导航到页面，设置120秒超时
            await self.page.goto(
                self.current_config.target_url,
                wait_until='networkidle',
                timeout=300000  # 120秒超时
            )
            self.logger.info("页面导航完成，开始等待页面完全加载...")

            # 第二步：等待页面完全加载
            load_success = await self._wait_for_page_ready()
            if not load_success:
                self.logger.warning("页面加载超时，但继续执行后续流程")

            # 第三步：立即检查登录状态
            self.logger.info("检查登录状态...")
            login_status = await self._check_login_status()

            # 设置初始登录状态
            self._last_login_status = login_status

            if login_status is False:
                self.logger.warning("检测到未登录状态")
                await self._handle_login_required()
            elif login_status is True:
                self.logger.info("检测到已登录状态")
            else:
                self.logger.info("无法确定登录状态")

            # 第四步：如果已登录，保存登录状态
            if login_status is True:
                await self._save_login_state()

            # 第五步：启动监控任务
            await self._start_message_monitoring()
            await self._start_login_monitoring()

            self.logger.info(f"成功完成网站加载流程: {self.current_config.target_url}")
            return True

        except Exception as e:
            self.logger.error(f"导航失败: {e}")
            return False
    
    async def send_message(self, message: str) -> bool:
        """
        发送消息到网页

        Args:
            message: 要发送的消息

        Returns:
            发送是否成功
        """
        if not self.page or not self.current_config:
            self.logger.error("页面或配置未初始化")
            return False

        try:
            # 如果有网站特定的流程处理器，使用它
            if self.workflow:
                self.logger.info("使用网站特定流程发送消息")
                return await self.workflow.send_message(message)

            # 否则使用通用流程
            self.logger.info("使用通用流程发送消息")
            selectors = self.current_config.selectors
            input_selector = selectors.get('input_box_selector')
            send_selector = selectors.get('send_button_selector')

            if not input_selector or not send_selector:
                self.logger.error("输入框或发送按钮选择器未配置")
                return False

            # 添加随机延时
            delay_range = self.current_config.anti_crawler.get('request_delay_ms', [1000, 3000])
            delay = random.randint(delay_range[0], delay_range[1]) / 1000
            await asyncio.sleep(delay)

            # 填入消息
            await self.page.fill(input_selector, message)
            await asyncio.sleep(0.5)  # 短暂延时

            # 点击发送按钮
            await self.page.click(send_selector)

            self.logger.info(f"成功发送消息: {message[:50]}...")
            return True

        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            return False
    
    async def get_messages(self) -> List[Dict[str, Any]]:
        """
        获取抓取到的消息列表
        
        Returns:
            消息列表
        """
        return self._messages.copy()
    
    async def save_page_html(self) -> Optional[str]:
        """
        保存当前页面的HTML到本地
        
        Returns:
            保存的文件路径，失败时返回None
        """
        if not self.page or not self.current_config:
            self.logger.error("页面或配置未初始化")
            return None
        
        try:
            # 获取页面HTML
            html_content = await self.page.content()
            
            # 确保保存目录存在
            save_path = Path(self.current_config.html_save_path)
            save_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存HTML文件
            with open(save_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            self.logger.info(f"页面HTML已保存到: {save_path}")
            return str(save_path)
            
        except Exception as e:
            self.logger.error(f"保存页面HTML失败: {e}")
            return None

    async def _save_login_state(self):
        """保存当前登录状态到文件"""
        if not self.context or not self.current_config:
            self.logger.warning("无法保存登录状态：context或config为空")
            return

        try:
            # 使用网站特定的存储目录
            site_name = self.current_config.site_name.lower()
            storage_dir = Path("storage") / site_name
            storage_dir.mkdir(parents=True, exist_ok=True)

            storage_file = storage_dir / self.current_config.storage.get('cookies_file', 'cookies.json')

            # 保存浏览器状态（包括cookies、localStorage等）
            await self.context.storage_state(path=str(storage_file))

            # 验证文件是否成功保存
            if storage_file.exists():
                file_size = storage_file.stat().st_size
                self.logger.info(f"✅ 登录状态已保存: {storage_file} ({file_size} 字节)")
            else:
                self.logger.error(f"❌ 登录状态保存失败：文件未创建")

        except Exception as e:
            self.logger.error(f"❌ 保存登录状态失败: {e}")

    async def _start_message_monitoring(self):
        """启动消息监控"""
        if self._monitoring_task:
            self._monitoring_task.cancel()
        
        self._monitoring_task = asyncio.create_task(self._monitor_messages())
        self.logger.info("消息监控已启动")
    
    async def _monitor_messages(self):
        """监控消息的后台任务"""
        while True:
            try:
                # 检查页面是否仍然有效
                if not self.page or not self.current_config or self.page.is_closed():
                    await asyncio.sleep(1)
                    continue

                await self._fetch_new_messages()
                await asyncio.sleep(1)  # 每秒检查一次
            except asyncio.CancelledError:
                self.logger.debug("消息监控任务被取消")
                break
            except Exception as e:
                error_msg = str(e).lower()
                if any(keyword in error_msg for keyword in ['execution context', 'navigation', 'target closed']):
                    self.logger.debug(f"页面导航中，暂停消息监控: {e}")
                    await asyncio.sleep(2)  # 页面导航时短暂等待
                else:
                    self.logger.error(f"消息监控出错: {e}")
                    await asyncio.sleep(5)  # 其他错误等待5秒再重试
    
    async def _fetch_new_messages(self):
        """抓取新消息"""
        if not self.page or not self.current_config or self.page.is_closed():
            return

        try:
            message_selector = self.current_config.selectors.get('message_box_selector')
            if not message_selector:
                return

            # 获取消息元素
            message_elements = await self.page.query_selector_all(message_selector)
            current_count = len(message_elements)

            # 如果有新消息
            if current_count > self._last_message_count:
                # 提取新消息的文本
                for i in range(self._last_message_count, current_count):
                    try:
                        element = message_elements[i]
                        text = await element.inner_text()

                        # 添加到消息列表
                        self._messages.append({
                            'text': text.strip(),
                            'timestamp': asyncio.get_event_loop().time(),
                            'index': len(self._messages)
                        })
                    except Exception as e:
                        self.logger.debug(f"提取消息文本失败: {e}")

                self._last_message_count = current_count

        except Exception as e:
            error_msg = str(e).lower()
            if any(keyword in error_msg for keyword in ['execution context', 'navigation', 'target closed']):
                self.logger.debug(f"页面导航中，跳过消息抓取: {e}")
            else:
                self.logger.debug(f"抓取消息失败: {e}")
    
    async def _start_login_monitoring(self):
        """启动登录状态监控"""
        if self._login_monitoring_task:
            self._login_monitoring_task.cancel()
        
        self._login_monitoring_task = asyncio.create_task(self._monitor_login_status())
        self.logger.info("登录状态监控已启动")
    
    async def _monitor_login_status(self):
        """监控登录状态的后台任务"""
        login_config = self.current_config.selectors.get('login_status', {})
        check_interval = login_config.get('check_interval_seconds', 60)
        initial_delay = login_config.get('initial_check_delay_seconds', 5)

        # 初始延迟，让页面有时间完全加载
        if initial_delay > 0:
            self.logger.debug(f"登录状态监控初始延迟 {initial_delay} 秒")
            await asyncio.sleep(initial_delay)

        while True:
            try:
                # 检查页面和配置是否仍然有效
                if not self.page or not self.current_config or self.page.is_closed():
                    self.logger.debug("页面或配置无效，暂停登录状态监控")
                    await asyncio.sleep(check_interval)
                    continue

                login_status = await self._check_login_status()

                # 检查登录状态是否发生变化
                if login_status != self._last_login_status and login_status is not None:
                    status_text = {True: "已登录", False: "未登录", None: "未知"}
                    self.logger.info(f"登录状态发生变化: {status_text.get(self._last_login_status, self._last_login_status)} -> {status_text.get(login_status, login_status)}")
                    self._last_login_status = login_status

                    # 调用状态变化回调
                    if self._login_status_changed_callback:
                        try:
                            if asyncio.iscoroutinefunction(self._login_status_changed_callback):
                                await self._login_status_changed_callback(login_status)
                            else:
                                self._login_status_changed_callback(login_status)
                        except Exception as e:
                            self.logger.error(f"登录状态变化回调失败: {e}")

                    # 如果变为已登录状态，保存登录状态
                    if login_status is True:
                        await self._save_login_state()

                # 如果检测到未登录状态，处理登录提醒（但只在状态变化时或首次检测时）
                if login_status is False and (self._last_login_status != login_status or self._last_login_status is None):
                    await self._handle_login_required()

                await asyncio.sleep(check_interval)
            except asyncio.CancelledError:
                self.logger.debug("登录状态监控任务被取消")
                break
            except Exception as e:
                error_msg = str(e).lower()
                if any(keyword in error_msg for keyword in ['execution context', 'navigation', 'target closed']):
                    self.logger.debug(f"页面导航中，暂停监控: {e}")
                    await asyncio.sleep(5)  # 页面导航时短暂等待
                else:
                    self.logger.error(f"登录状态监控出错: {e}")
                    await asyncio.sleep(30)  # 其他错误等待30秒再重试
    
    async def _check_login_status(self) -> Optional[bool]:
        """
        检查当前登录状态

        Returns:
            True: 已登录, False: 未登录, None: 无法确定
        """
        if not self.page or not self.current_config:
            return None

        try:
            # 检查页面是否仍然有效
            if self.page.is_closed():
                self.logger.debug("页面已关闭，无法检查登录状态")
                return None

            login_config = self.current_config.selectors.get('login_status', {})

            # 检查已登录指示器
            logged_in_selector = login_config.get('logged_in_indicator')
            if logged_in_selector:
                try:
                    logged_in_element = await self.page.query_selector(logged_in_selector)
                    if logged_in_element:
                        self.logger.debug("检测到已登录状态")
                        return True
                except Exception as e:
                    self.logger.debug(f"检查已登录指示器失败: {e}")

            # 检查未登录指示器
            logged_out_selector = login_config.get('logged_out_indicator')
            if logged_out_selector:
                try:
                    logged_out_element = await self.page.query_selector(logged_out_selector)
                    if logged_out_element:
                        # 检查是否需要验证按钮文本
                        expected_text = login_config.get('logged_out_text')
                        if expected_text:
                            try:
                                button_text = await logged_out_element.inner_text()
                                if expected_text in button_text:
                                    self.logger.debug(f"检测到登出状态，文本匹配: '{expected_text}'")
                                    return False
                                else:
                                    self.logger.debug(f"登录按钮文本不匹配，期望: '{expected_text}', 实际: '{button_text}'")
                            except Exception as e:
                                self.logger.debug(f"获取按钮文本失败: {e}")
                                # 即使获取文本失败，如果元素存在也认为是未登录状态
                                return False
                        else:
                            # 不需要验证文本，直接返回未登录状态
                            return False
                except Exception as e:
                    self.logger.debug(f"检查未登录指示器失败: {e}")

            return None

        except Exception as e:
            # 处理常见的页面导航错误
            error_msg = str(e).lower()
            if any(keyword in error_msg for keyword in ['execution context', 'navigation', 'target closed']):
                self.logger.debug(f"页面导航中，跳过登录状态检查: {e}")
                return None
            else:
                self.logger.error(f"检查登录状态失败: {e}")
                return None

    async def _wait_for_page_ready(self, timeout_seconds: int = 120) -> bool:
        """
        等待页面完全加载就绪

        Args:
            timeout_seconds: 超时时间（秒）

        Returns:
            页面是否加载成功
        """
        try:
            self.logger.info(f"等待页面加载完成，超时时间: {timeout_seconds}秒")

            # 等待页面基本元素加载
            await self.page.wait_for_load_state('domcontentloaded', timeout=timeout_seconds * 1000)
            self.logger.debug("DOM内容加载完成")

            # 等待网络空闲
            await self.page.wait_for_load_state('networkidle', timeout=timeout_seconds * 1000)
            self.logger.debug("网络空闲状态达成")

            # 额外等待一些关键元素
            await self._wait_for_key_elements()

            self.logger.info("页面加载完成")
            return True

        except Exception as e:
            self.logger.warning(f"页面加载等待超时或失败: {e}")
            return False

    async def _wait_for_key_elements(self, timeout_ms: int = 10000):
        """
        等待关键页面元素加载

        Args:
            timeout_ms: 超时时间（毫秒）
        """
        if not self.current_config:
            return

        try:
            selectors = self.current_config.selectors

            # 尝试等待输入框或登录按钮出现（任一即可）
            key_selectors = []

            if selectors.get('input_box_selector'):
                key_selectors.append(selectors['input_box_selector'])

            login_config = selectors.get('login_status', {})
            if login_config.get('logged_out_indicator'):
                key_selectors.append(login_config['logged_out_indicator'])
            if login_config.get('logged_in_indicator'):
                key_selectors.append(login_config['logged_in_indicator'])

            # 等待任一关键元素出现
            if key_selectors:
                element_found = False
                for selector in key_selectors:
                    try:
                        await self.page.wait_for_selector(selector, timeout=timeout_ms, state='visible')
                        self.logger.debug(f"关键元素已加载: {selector}")
                        element_found = True
                        break
                    except:
                        continue

                if not element_found:
                    self.logger.debug("未找到任何关键元素，但继续执行")

        except Exception as e:
            self.logger.debug(f"等待关键元素失败: {e}")

    async def _handle_login_required(self):
        """
        处理需要登录的情况
        """
        try:
            # 防重复提醒：60秒内最多提醒一次
            current_time = asyncio.get_event_loop().time()
            if current_time - self._last_login_prompt_time < 60:
                self.logger.debug("登录提醒间隔未到，跳过本次提醒")
                return

            self.logger.info("处理登录提醒流程")

            # 获取登录配置
            login_config = self.current_config.selectors.get('login_status', {})
            logged_out_selector = login_config.get('logged_out_indicator')

            if logged_out_selector:
                # 确认登录按钮存在
                login_button = await self.page.query_selector(logged_out_selector)
                if login_button:
                    # 获取按钮文本进行验证
                    button_text = await login_button.inner_text()
                    expected_text = login_config.get('logged_out_text', '登录')

                    if expected_text in button_text:
                        self.logger.info(f"确认检测到登录按钮，文本: '{button_text}'")

                        # 更新上次提醒时间
                        self._last_login_prompt_time = current_time

                        # 触发登录提醒事件（可以被GUI或API监听）
                        await self._trigger_login_required_event()
                    else:
                        self.logger.debug(f"登录按钮文本不匹配，期望: '{expected_text}', 实际: '{button_text}'")
                else:
                    self.logger.debug("未找到登录按钮元素")

        except Exception as e:
            self.logger.error(f"处理登录提醒失败: {e}")

    async def _trigger_login_required_event(self):
        """
        触发登录需求事件
        """
        self.logger.warning("⚠️  检测到需要登录，请手动登录后继续操作")

        # 调用登录需求回调函数
        if self._login_required_callback:
            try:
                if asyncio.iscoroutinefunction(self._login_required_callback):
                    await self._login_required_callback()
                else:
                    self._login_required_callback()
            except Exception as e:
                self.logger.error(f"登录需求回调失败: {e}")
