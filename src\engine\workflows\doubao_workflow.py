"""
豆包网站特定流程处理器
实现豆包网站的专属交互流程
"""

import asyncio
from typing import Optional

from ..site_workflow import BaseWebWorkflow


class DoubaoWorkflow(BaseWebWorkflow):
    """
    豆包网站特定流程处理器
    实现豆包网站的登录检查、新对话创建、深度思考设置等专属流程
    """
    
    async def check_login_status(self) -> Optional[bool]:
        """
        检查豆包登录状态
        通过检查头像按钮来确认登录状态
        """
        try:
            login_config = self.config.selectors.get('login_status', {})
            
            # 检查已登录指示器（头像按钮）
            logged_in_selector = login_config.get('logged_in_indicator')
            if logged_in_selector:
                element = await self.page.query_selector(logged_in_selector)
                if element:
                    self.logger.debug("检测到已登录状态（头像按钮存在）")
                    self.is_logged_in = True
                    return True
            
            # 检查未登录指示器（登录按钮）
            logged_out_selector = login_config.get('logged_out_indicator')
            if logged_out_selector:
                element = await self.page.query_selector(logged_out_selector)
                if element:
                    expected_text = login_config.get('logged_out_text', '登录')
                    button_text = await element.inner_text()
                    if expected_text in button_text:
                        self.logger.debug("检测到未登录状态（登录按钮存在）")
                        self.is_logged_in = False
                        return False
            
            return None
            
        except Exception as e:
            self.logger.error(f"检查登录状态失败: {e}")
            return None
    
    async def handle_login_required(self) -> bool:
        """
        处理豆包登录需求
        提示用户手动登录
        """
        self.logger.warning("⚠️  检测到需要登录，请手动登录后继续操作")
        return False  # 需要用户手动登录
    
    async def prepare_for_message_sending(self) -> bool:
        """
        豆包发送消息前的准备工作
        流程：检查登录状态 -> 处理侧边栏状态 -> 确保在新对话中 -> 设置深度思考模式
        """
        try:
            # 1. 首先检查登录状态
            login_status = await self.check_login_status()
            if login_status is False:
                await self.handle_login_required()
                return False
            elif login_status is None:
                self.logger.warning("无法确定登录状态")
                return False

            # 2. 处理侧边栏状态和新对话
            if not await self._handle_sidebar_and_new_chat():
                return False

            # 3. 检查并设置深度思考模式
            await self._ensure_deep_thinking_enabled()

            return True

        except Exception as e:
            self.logger.error(f"准备发送消息失败: {e}")
            return False

    async def _handle_sidebar_and_new_chat(self) -> bool:
        """
        处理侧边栏状态和新对话逻辑
        流程：
        1. 检查侧边栏是否打开，如果打开则关闭
        2. 检查是否在新对话中，如果不是则点击新对话按钮
        """
        try:
            sidebar_elements = self.config.selectors.get('sidebar_elements', {})
            chat_sidebar_selector = sidebar_elements.get('chat_sidebar')

            if not chat_sidebar_selector:
                self.logger.warning("侧边栏选择器未配置，跳过侧边栏处理")
                return True

            # 1. 检查侧边栏状态
            self.logger.info("检查侧边栏状态...")
            sidebar_element = await self.page.query_selector(chat_sidebar_selector)

            if not sidebar_element:
                self.logger.warning("未找到侧边栏元素")
                return True

            # 2. 检查侧边栏是否打开（通过查找关闭按钮）
            close_btn_selector = sidebar_elements.get('sidebar_close_btn')
            if close_btn_selector:
                close_btn = await sidebar_element.query_selector(close_btn_selector)
                if close_btn:
                    self.logger.info("侧边栏已打开，正在关闭...")
                    await close_btn.click()
                    await asyncio.sleep(0.5)  # 等待关闭动画
                    self.logger.info("侧边栏已关闭")
                else:
                    self.logger.info("侧边栏已关闭")

            # 3. 检查新对话按钮状态
            return await self._check_and_click_new_chat()

        except Exception as e:
            self.logger.error(f"处理侧边栏和新对话失败: {e}")
            return False

    async def _check_and_click_new_chat(self) -> bool:
        """
        检查并点击新对话按钮
        """
        try:
            sidebar_elements = self.config.selectors.get('sidebar_elements', {})
            closed_status_btn_selector = sidebar_elements.get('sidebar_closed_status_btn')
            new_chat_pattern = sidebar_elements.get('new_chat_button_pattern', 'create-chat-')

            if not closed_status_btn_selector:
                self.logger.warning("侧边栏关闭状态按钮选择器未配置")
                return True

            # 1. 找到侧边栏关闭状态按钮
            status_btn = await self.page.query_selector(closed_status_btn_selector)
            if not status_btn:
                self.logger.warning("未找到侧边栏关闭状态按钮")
                return True

            # 2. 查找新对话按钮（在同级元素中搜索）
            self.logger.info("查找新对话按钮...")

            # 获取父元素
            parent_element = await status_btn.evaluate("element => element.parentElement")
            if not parent_element:
                self.logger.warning("无法获取父元素")
                return True

            # 在父元素中查找包含 create-chat- 的按钮
            new_chat_buttons = await self.page.query_selector_all(f"button[class*='{new_chat_pattern}']")

            if not new_chat_buttons:
                self.logger.warning("未找到新对话按钮")
                return True

            # 3. 检查新对话按钮是否可用
            new_chat_btn = new_chat_buttons[0]  # 取第一个匹配的按钮

            # 检查按钮是否被禁用
            is_disabled = await new_chat_btn.evaluate("element => element.disabled || element.classList.contains('cursor-not-allowed') || element.classList.contains('opacity-30')")

            if is_disabled:
                self.logger.info("新对话按钮已禁用，说明当前处于新对话中")
                return True
            else:
                self.logger.info("新对话按钮可用，正在点击...")
                await new_chat_btn.click()
                await asyncio.sleep(1)  # 等待页面响应
                self.logger.info("成功点击新对话按钮")
                return True

        except Exception as e:
            self.logger.error(f"检查和点击新对话按钮失败: {e}")
            return False
    
    async def _ensure_deep_thinking_enabled(self) -> bool:
        """
        确保深度思考模式已启用
        流程：
        1. 先定位 data-testid="upload-file-input" 附件上传元素
        2. 找到附件上传的同级下一个button元素（深度思考按钮），确认标志：aria-haspopup="dialog"
        3. 找到深度思考按钮下级元素 class="semi-button-content-right"
        4. 确认文本内容是否是"深度思考: 自动"
        5. 如果不是，点击按钮打开菜单
        6. 点击第一个 menu-item
        """
        try:
            self.logger.info("开始检查深度思考模式...")

            interaction_elements = self.config.selectors.get('interaction_elements', {})
            upload_input_selector = interaction_elements.get('upload_file_input')
            status_content_selector = interaction_elements.get('deep_thinking_status_content')
            menu_items_selector = interaction_elements.get('deep_thinking_menu_items')

            self.logger.info(f"配置选择器 - 附件上传: {upload_input_selector}")
            self.logger.info(f"配置选择器 - 状态内容: {status_content_selector}")
            self.logger.info(f"配置选择器 - 菜单项: {menu_items_selector}")

            if not upload_input_selector:
                self.logger.warning("附件上传选择器未配置，跳过深度思考检查")
                return True

            # 1. 定位附件上传元素
            self.logger.info("步骤1: 定位附件上传元素...")
            upload_element = await self.page.query_selector(upload_input_selector)
            if not upload_element:
                self.logger.warning("步骤1失败: 未找到附件上传元素")
                return True
            self.logger.info("步骤1成功: 找到附件上传元素")

            # 2. 找到附件上传的同级下一个button元素（深度思考按钮）
            self.logger.info("步骤2: 查找深度思考按钮...")
            # 使用JavaScript来查找同级的下一个button元素，并确认aria-haspopup="dialog"
            deep_thinking_element_handle = await upload_element.evaluate_handle("""
                element => {
                    let nextSibling = element.nextElementSibling;
                    while (nextSibling) {
                        if (nextSibling.tagName === 'BUTTON' && nextSibling.getAttribute('aria-haspopup') === 'dialog') {
                            return nextSibling;
                        }
                        nextSibling = nextSibling.nextElementSibling;
                    }
                    return null;
                }
            """)

            if not deep_thinking_element_handle:
                self.logger.warning("步骤2失败: 未找到深度思考按钮元素")
                return True
            self.logger.info("步骤2成功: 找到深度思考按钮元素")

            # 3. 找到下级元素 class="semi-button-content-right"
            self.logger.info("步骤3: 查找深度思考状态内容元素...")
            if status_content_selector:
                status_content = await deep_thinking_element_handle.query_selector(status_content_selector)
                if status_content:
                    self.logger.info("步骤3成功: 找到深度思考状态内容元素")

                    # 4. 确认文本内容是否是"深度思考: 自动"
                    self.logger.info("步骤4: 读取深度思考状态文本...")
                    status_text = await status_content.inner_text()
                    self.logger.info(f"步骤4成功: 当前深度思考状态文本: '{status_text}'")

                    if "深度思考: 自动" in status_text:
                        self.logger.info("深度思考已设置为自动模式，无需修改")
                        return True
                    else:
                        self.logger.info("深度思考未设置为自动模式，需要修改")
                else:
                    self.logger.warning("步骤3失败: 未找到深度思考状态内容元素")
            else:
                self.logger.warning("步骤3跳过: 状态内容选择器未配置")

            # 5. 如果不是自动模式，点击按钮打开菜单
            self.logger.info("步骤5: 点击深度思考按钮打开菜单...")
            await deep_thinking_element_handle.click()
            self.logger.info("步骤5成功: 已点击深度思考按钮")

            # 等待菜单出现
            self.logger.info("等待菜单出现...")
            await asyncio.sleep(0.5)

            # 6. 点击第一个 menu-item
            self.logger.info("步骤6: 查找并点击第一个菜单项...")
            if menu_items_selector:
                menu_items = await self.page.query_selector_all(menu_items_selector)
                self.logger.info(f"找到 {len(menu_items) if menu_items else 0} 个菜单项")

                if menu_items and len(menu_items) >= 1:
                    first_menu_item = menu_items[0]
                    self.logger.info("点击第一个菜单项（自动模式）...")
                    await first_menu_item.click()
                    await asyncio.sleep(0.5)  # 等待设置生效
                    self.logger.info("步骤6成功: 成功设置深度思考为自动模式")
                    return True
                else:
                    self.logger.warning("步骤6失败: 未找到深度思考菜单项")
            else:
                self.logger.warning("步骤6跳过: 菜单项选择器未配置")

            return False

        except Exception as e:
            self.logger.error(f"设置深度思考模式失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False

    async def wait_for_response_and_extract(self, message_index: int = 1) -> str:
        """
        等待豆包回复并提取回复内容

        Args:
            message_index: 发送的消息序号（从1开始）

        Returns:
            str: 提取到的回复内容
        """
        try:
            self.logger.info(f"开始等待第{message_index}条消息的回复...")

            # 获取消息提取相关选择器
            extraction_selectors = self.config.selectors.get('message_extraction', {})
            message_list_selector = extraction_selectors.get('message_list')
            inter_container_selector = extraction_selectors.get('inter_container')
            message_container_selector = extraction_selectors.get('message_container')
            receive_message_selector = extraction_selectors.get('receive_message')
            answer_action_bar_selector = extraction_selectors.get('answer_action_bar')
            message_action_copy_selector = extraction_selectors.get('message_action_copy')
            message_text_content_selector = extraction_selectors.get('message_text_content')

            self.logger.info("消息提取选择器配置:")
            self.logger.info(f"  消息列表: {message_list_selector}")
            self.logger.info(f"  中间容器: {inter_container_selector}")
            self.logger.info(f"  消息容器: {message_container_selector}")
            self.logger.info(f"  接收消息: {receive_message_selector}")
            self.logger.info(f"  操作栏: {answer_action_bar_selector}")
            self.logger.info(f"  复制按钮: {message_action_copy_selector}")
            self.logger.info(f"  文本内容: {message_text_content_selector}")

            if not all([message_list_selector, inter_container_selector, message_container_selector,
                       receive_message_selector, message_text_content_selector]):
                self.logger.error("消息提取选择器配置不完整")
                return ""

            # 首先等待页面加载和回复开始
            self.logger.info("等待页面加载和回复开始...")
            await asyncio.sleep(3)  # 给页面一些时间来处理消息发送

            # 步骤1: 等待并定位消息列表
            self.logger.info("步骤1: 等待并定位消息列表...")
            message_list = await self._wait_for_message_list(message_list_selector)
            if not message_list:
                self.logger.error("步骤1失败: 未找到消息列表")
                return ""
            self.logger.info("步骤1成功: 找到消息列表")

            # 步骤2: 等待并获取回复容器
            self.logger.info("步骤2: 等待并获取回复容器...")
            reply_container = await self._wait_for_reply_container(
                message_list, inter_container_selector, message_container_selector,
                receive_message_selector, message_index
            )

            if not reply_container:
                self.logger.error("步骤2失败: 未找到回复容器")
                return ""
            self.logger.info("步骤2成功: 找到回复容器")

            # 步骤3: 周期性检查回复是否完成并提取内容
            self.logger.info("步骤3: 开始周期性检查回复完成状态...")
            reply_text = await self._wait_and_extract_reply(
                reply_container, message_action_copy_selector, message_text_content_selector
            )

            if reply_text:
                self.logger.info(f"步骤3成功: 提取到回复内容，长度: {len(reply_text)} 字符")
                self.logger.info(f"回复内容预览: {reply_text[:100]}...")
                return reply_text
            else:
                self.logger.error("步骤3失败: 未能提取到回复内容")
                return ""

        except Exception as e:
            self.logger.error(f"等待回复并提取内容失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            return ""

    async def _wait_for_message_list(self, message_list_selector: str, max_wait_time: int = 30):
        """
        等待消息列表出现

        Args:
            message_list_selector: 消息列表选择器
            max_wait_time: 最大等待时间（秒）

        Returns:
            消息列表元素或None
        """
        try:
            wait_interval = 1  # 每秒检查一次
            waited_time = 0

            while waited_time < max_wait_time:
                message_list = await self.page.query_selector(message_list_selector)
                if message_list:
                    return message_list

                self.logger.info(f"等待消息列表出现... 已等待 {waited_time} 秒")
                await asyncio.sleep(wait_interval)
                waited_time += wait_interval

            self.logger.warning(f"等待消息列表超时（{max_wait_time}秒）")
            return None

        except Exception as e:
            self.logger.error(f"等待消息列表失败: {e}")
            return None

    async def _wait_for_reply_container(self, message_list, inter_container_selector: str,
                                      message_container_selector: str, receive_message_selector: str,
                                      message_index: int, max_wait_time: int = 60):
        """
        等待回复容器出现

        Args:
            message_list: 消息列表元素
            inter_container_selector: 中间容器选择器
            message_container_selector: 消息容器选择器
            receive_message_selector: 接收消息选择器
            message_index: 消息索引
            max_wait_time: 最大等待时间（秒）

        Returns:
            回复容器元素或None
        """
        try:
            # 目标回复容器的索引
            target_reply_index = message_index * 2 - 1
            self.logger.info(f"目标回复容器索引: {target_reply_index}")
            self.logger.info(f"使用的选择器:")
            self.logger.info(f"  inter_container_selector: {inter_container_selector}")
            self.logger.info(f"  message_container_selector: {message_container_selector}")
            self.logger.info(f"  receive_message_selector: {receive_message_selector}")

            wait_interval = 2  # 每2秒检查一次
            waited_time = 0

            while waited_time < max_wait_time:
                # 定位inter-容器
                self.logger.info(f"查找inter-容器，选择器: {inter_container_selector}")
                inter_container = await message_list.query_selector(inter_container_selector)
                if not inter_container:
                    self.logger.info(f"未找到inter-容器，已等待 {waited_time} 秒")
                    await asyncio.sleep(wait_interval)
                    waited_time += wait_interval
                    continue

                self.logger.info(f"找到inter-容器，查找直接子级消息容器，选择器: {message_container_selector}")

                # 获取直接子级的消息容器（使用 :scope > 限制只查找直接子元素）
                direct_child_selector = f":scope > {message_container_selector}"
                self.logger.info(f"使用直接子级选择器: {direct_child_selector}")
                message_containers = await inter_container.query_selector_all(direct_child_selector)

                # 如果直接子级查找失败，尝试不使用 :scope
                if len(message_containers) == 0:
                    self.logger.info("直接子级查找失败，尝试不使用 :scope 前缀...")
                    message_containers = await inter_container.query_selector_all(message_container_selector)
                self.logger.info(f"当前消息容器数量: {len(message_containers)}")

                # 详细检查每个容器
                if len(message_containers) > 0:
                    self.logger.info(f"检查所有 {len(message_containers)} 个容器的详细信息:")
                    receive_containers = []  # 记录包含receive_message的容器

                    for i, container in enumerate(message_containers):
                        try:
                            # 获取容器的class属性
                            class_attr = await container.get_attribute('class')
                            # 检查是否包含receive_message
                            receive_msg = await container.query_selector(receive_message_selector)
                            has_receive = "是" if receive_msg else "否"

                            if receive_msg:
                                receive_containers.append(i)

                            # 只显示前10个和包含receive_message的容器
                            if i < 10 or receive_msg:
                                self.logger.info(f"  容器[{i}]: class='{class_attr}', 包含receive_message: {has_receive}")
                        except Exception as e:
                            self.logger.info(f"  容器[{i}]: 检查失败 - {e}")

                    self.logger.info(f"包含receive_message的容器索引: {receive_containers}")

                    # 重新计算目标索引
                    if len(receive_containers) >= message_index:
                        actual_target_index = receive_containers[message_index - 1]
                        self.logger.info(f"重新计算的目标索引: {actual_target_index} (第{message_index}个回复容器)")

                        if actual_target_index < len(message_containers):
                            reply_container = message_containers[actual_target_index]
                            receive_message = await reply_container.query_selector(receive_message_selector)
                            if receive_message:
                                self.logger.info("✅ 使用重新计算的索引找到回复容器！")

                                # 调试：检查找到的receive_message元素
                                try:
                                    element_html = await receive_message.inner_html()
                                    self.logger.info(f"找到的receive_message元素HTML结构: {element_html[:300]}...")

                                    # 检查是否包含复制按钮
                                    copy_button_test = await receive_message.query_selector('[data-testid="message_action_copy"]')
                                    if copy_button_test:
                                        self.logger.info("✅ 在receive_message元素中找到了复制按钮！")
                                    else:
                                        self.logger.info("❌ 在receive_message元素中没有找到复制按钮")

                                        # 尝试在整个消息容器中查找复制按钮
                                        container_copy_button = await reply_container.query_selector('[data-testid="message_action_copy"]')
                                        if container_copy_button:
                                            self.logger.info("✅ 在消息容器中找到了复制按钮！")
                                        else:
                                            self.logger.info("❌ 在消息容器中也没有找到复制按钮")

                                except Exception as e:
                                    self.logger.info(f"调试信息获取失败: {e}")

                                return receive_message

                # 检查是否有足够的容器
                if len(message_containers) > target_reply_index:
                    self.logger.info(f"检查目标容器[{target_reply_index}]是否为回复消息...")
                    reply_container = message_containers[target_reply_index]

                    # 获取目标容器的详细信息
                    try:
                        target_class = await reply_container.get_attribute('class')
                        self.logger.info(f"目标容器[{target_reply_index}] class: '{target_class}'")
                    except Exception as e:
                        self.logger.info(f"无法获取目标容器class: {e}")

                    # 确认是回复消息
                    self.logger.info(f"在目标容器中查找receive_message，选择器: {receive_message_selector}")
                    receive_message = await reply_container.query_selector(receive_message_selector)
                    if receive_message:
                        self.logger.info("✅ 找到回复容器！")
                        return receive_message
                    else:
                        self.logger.info("❌ 目标容器不包含receive_message，继续等待...")
                else:
                    self.logger.info(f"容器数量不足，需要 > {target_reply_index}，当前: {len(message_containers)}")

                await asyncio.sleep(wait_interval)
                waited_time += wait_interval

            self.logger.warning(f"等待回复容器超时（{max_wait_time}秒）")
            return None

        except Exception as e:
            self.logger.error(f"等待回复容器失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            return None

    async def _wait_and_extract_reply(self, receive_message_element, message_action_copy_selector: str,
                                    message_text_content_selector: str, max_wait_time: int = 120) -> str:
        """
        周期性检查回复是否完成并提取内容

        Args:
            receive_message_element: 接收消息的元素
            message_action_copy_selector: 复制按钮选择器
            message_text_content_selector: 消息文本内容选择器
            max_wait_time: 最大等待时间（秒）

        Returns:
            提取到的回复内容
        """
        try:
            self.logger.info("开始周期性检查回复完成状态...")

            wait_interval = 3  # 每3秒检查一次
            waited_time = 0
            last_content = ""
            stable_count = 0  # 内容稳定次数计数

            while waited_time < max_wait_time:
                # 检查复制按钮是否出现（回复完成的标志）
                self.logger.info(f"在receive_message元素内查找复制按钮，选择器: {message_action_copy_selector}")
                copy_button = await receive_message_element.query_selector(message_action_copy_selector)

                if copy_button:
                    self.logger.info("✅ 检测到复制按钮，回复已完成")

                    # 提取最终内容
                    message_content = await receive_message_element.query_selector(message_text_content_selector)
                    if message_content:
                        reply_text = await message_content.inner_text()
                        self.logger.info("成功提取回复内容")
                        return reply_text.strip()
                    else:
                        self.logger.warning("复制按钮存在但未找到文本内容")
                        return ""
                else:
                    self.logger.info("❌ 复制按钮未找到，继续等待...")

                # 如果复制按钮还没出现，检查当前内容
                # 先尝试使用指定的文本内容选择器
                self.logger.info(f"在receive_message元素内查找文本内容，选择器: {message_text_content_selector}")
                message_content = await receive_message_element.query_selector(message_text_content_selector)

                current_content = ""
                if message_content:
                    current_content = await message_content.inner_text()
                    current_content = current_content.strip()
                    self.logger.info(f"使用指定选择器找到文本内容: {current_content[:50]}...")
                else:
                    # 如果指定选择器找不到，直接从receive_message元素获取文本
                    self.logger.info("指定选择器未找到，尝试直接从receive_message元素获取文本...")
                    current_content = await receive_message_element.inner_text()
                    current_content = current_content.strip()
                    if current_content:
                        self.logger.info(f"直接从receive_message获取到文本: {current_content[:50]}...")
                    else:
                        # 调试：输出receive_message元素的HTML结构
                        try:
                            element_html = await receive_message_element.inner_html()
                            self.logger.info(f"receive_message元素HTML结构: {element_html[:200]}...")
                        except Exception as e:
                            self.logger.info(f"无法获取元素HTML: {e}")
                        self.logger.info("receive_message元素内容为空，继续等待...")

                if current_content:
                    # 检查内容是否有变化
                    if current_content == last_content:
                        stable_count += 1
                        self.logger.info(f"内容稳定 {stable_count} 次: {current_content[:50]}...")

                        # 如果内容连续3次没有变化，可能回复已经完成但复制按钮还没出现
                        if stable_count >= 3:
                            self.logger.info("内容已稳定，可能回复完成")
                            # 再等待一次检查复制按钮
                            await asyncio.sleep(wait_interval)
                            self.logger.info(f"内容稳定后再次检查复制按钮，选择器: {message_action_copy_selector}")
                            copy_button = await receive_message_element.query_selector(message_action_copy_selector)
                            if copy_button:
                                self.logger.info("✅ 确认回复完成")
                                return current_content
                            else:
                                self.logger.info("❌ 复制按钮仍未出现，继续等待...")
                                stable_count = 0  # 重置计数
                    else:
                        stable_count = 0
                        self.logger.info(f"检测到内容更新: {current_content[:50]}...")
                        last_content = current_content
                else:
                    self.logger.info("等待消息内容出现...")

                await asyncio.sleep(wait_interval)
                waited_time += wait_interval

                if waited_time % 15 == 0:  # 每15秒输出一次等待状态
                    self.logger.info(f"已等待 {waited_time} 秒，继续等待回复完成...")

            # 超时处理
            self.logger.warning(f"等待回复完成超时（{max_wait_time}秒）")

            # 尝试提取当前内容作为最终结果
            if last_content:
                self.logger.info("返回超时前的最后内容")
                return last_content
            else:
                return ""

        except Exception as e:
            self.logger.error(f"周期性检查回复失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            return ""


    async def send_message(self, message: str) -> bool:
        """
        发送消息到豆包
        完整流程：准备工作 -> 输入消息 -> 发送
        """
        try:
            # 1. 准备工作
            if not await self.prepare_for_message_sending():
                return False
            
            # 2. 在输入框中输入消息
            input_selector = self.config.selectors.get('input_box_selector')
            if not input_selector:
                self.logger.error("输入框选择器未配置")
                return False
            
            self.logger.info(f"在输入框中输入消息: {message[:50]}...")
            
            # 等待输入框出现
            if not await self.wait_for_element(input_selector):
                self.logger.error("输入框未找到")
                return False
            
            # 清空输入框并输入消息
            await self.page.fill(input_selector, message)
            await asyncio.sleep(0.5)  # 等待输入完成
            
            # 3. 点击发送按钮
            send_selector = self.config.selectors.get('send_button_selector')
            if not send_selector:
                self.logger.error("发送按钮选择器未配置")
                return False
            
            self.logger.info("点击发送按钮...")
            success = await self.click_element(send_selector)
            
            if success:
                self.logger.info("消息发送成功")
                return True
            else:
                self.logger.error("点击发送按钮失败")
                return False
                
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            return False

    async def send_message_and_get_response(self, message: str) -> str:
        """发送消息并获取回复"""
        try:
            # 确保在新对话中
            if not await self._check_and_click_new_chat():
                return "无法创建新对话"

            # 确保深度思考已启用
            if not await self._ensure_deep_thinking_enabled():
                self.logger.warning("深度思考模式设置可能失败，但继续执行")

            # 记录消息序号（简单实现，每次都假设是第一条消息）
            # 在实际应用中，可能需要更复杂的消息计数逻辑
            message_index = 1

            # 发送消息
            if not await self.send_message(message):
                return "发送消息失败"

            # 等待并提取回复
            self.logger.info("消息发送成功，开始等待回复...")
            reply = await self.wait_for_response_and_extract(message_index)

            if reply:
                self.logger.info("成功获取到回复")
                return reply
            else:
                self.logger.error("未能获取到回复")
                return "获取回复失败"

        except Exception as e:
            self.logger.error(f"发送消息并获取回复失败: {e}")
            return f"操作失败: {e}"
