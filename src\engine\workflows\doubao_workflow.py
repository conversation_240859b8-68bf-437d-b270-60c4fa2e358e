"""
豆包网站特定流程处理器
实现豆包网站的专属交互流程
"""

import asyncio
from typing import Optional

from ..site_workflow import BaseWebWorkflow


class DoubaoWorkflow(BaseWebWorkflow):
    """
    豆包网站特定流程处理器
    实现豆包网站的登录检查、新对话创建、深度思考设置等专属流程
    """
    
    async def check_login_status(self) -> Optional[bool]:
        """
        检查豆包登录状态
        通过检查头像按钮来确认登录状态
        """
        try:
            login_config = self.config.selectors.get('login_status', {})
            
            # 检查已登录指示器（头像按钮）
            logged_in_selector = login_config.get('logged_in_indicator')
            if logged_in_selector:
                element = await self.page.query_selector(logged_in_selector)
                if element:
                    self.logger.debug("检测到已登录状态（头像按钮存在）")
                    self.is_logged_in = True
                    return True
            
            # 检查未登录指示器（登录按钮）
            logged_out_selector = login_config.get('logged_out_indicator')
            if logged_out_selector:
                element = await self.page.query_selector(logged_out_selector)
                if element:
                    expected_text = login_config.get('logged_out_text', '登录')
                    button_text = await element.inner_text()
                    if expected_text in button_text:
                        self.logger.debug("检测到未登录状态（登录按钮存在）")
                        self.is_logged_in = False
                        return False
            
            return None
            
        except Exception as e:
            self.logger.error(f"检查登录状态失败: {e}")
            return None
    
    async def handle_login_required(self) -> bool:
        """
        处理豆包登录需求
        提示用户手动登录
        """
        self.logger.warning("⚠️  检测到需要登录，请手动登录后继续操作")
        return False  # 需要用户手动登录
    
    async def prepare_for_message_sending(self) -> bool:
        """
        豆包发送消息前的准备工作
        包括：检查登录状态 -> 点击新对话 -> 设置深度思考模式
        """
        try:
            # 1. 首先检查登录状态
            login_status = await self.check_login_status()
            if login_status is False:
                await self.handle_login_required()
                return False
            elif login_status is None:
                self.logger.warning("无法确定登录状态")
                return False
            
            # 2. 点击新对话按钮
            self.logger.info("点击新对话按钮...")
            new_chat_selector = self.config.selectors.get('interaction_elements', {}).get('new_chat_button')
            if new_chat_selector:
                success = await self.click_element(new_chat_selector)
                if success:
                    self.logger.info("成功点击新对话按钮")
                    await asyncio.sleep(1)  # 等待页面响应
                else:
                    self.logger.warning("点击新对话按钮失败")
            
            # 3. 检查并设置深度思考模式
            await self._ensure_deep_thinking_enabled()
            
            return True
            
        except Exception as e:
            self.logger.error(f"准备发送消息失败: {e}")
            return False
    
    async def _ensure_deep_thinking_enabled(self) -> bool:
        """
        确保深度思考模式已启用
        检查当前状态，如果不是"自动"模式则设置为自动
        """
        try:
            interaction_elements = self.config.selectors.get('interaction_elements', {})
            deep_thinking_button = interaction_elements.get('deep_thinking_button')
            deep_thinking_status = interaction_elements.get('deep_thinking_status')
            
            if not deep_thinking_button or not deep_thinking_status:
                self.logger.debug("深度思考相关选择器未配置")
                return True
            
            # 检查当前深度思考状态
            status_text = await self.get_element_text(deep_thinking_status)
            if status_text and "深度思考: 自动" in status_text:
                self.logger.info("深度思考已启用")
                return True
            
            self.logger.info("深度思考未启用，正在设置...")
            
            # 点击深度思考按钮打开菜单
            success = await self.click_element(deep_thinking_button)
            if not success:
                self.logger.warning("点击深度思考按钮失败")
                return False
            
            # 等待菜单出现
            await asyncio.sleep(0.5)
            
            # 点击"自动"选项
            auto_option_selector = interaction_elements.get('deep_thinking_menu_auto')
            if auto_option_selector:
                success = await self.click_element(auto_option_selector)
                if success:
                    self.logger.info("成功设置深度思考为自动模式")
                    await asyncio.sleep(0.5)  # 等待设置生效
                    return True
                else:
                    self.logger.warning("点击深度思考自动选项失败")
            
            return False
            
        except Exception as e:
            self.logger.error(f"设置深度思考模式失败: {e}")
            return False
    
    async def send_message(self, message: str) -> bool:
        """
        发送消息到豆包
        完整流程：准备工作 -> 输入消息 -> 发送
        """
        try:
            # 1. 准备工作
            if not await self.prepare_for_message_sending():
                return False
            
            # 2. 在输入框中输入消息
            input_selector = self.config.selectors.get('input_box_selector')
            if not input_selector:
                self.logger.error("输入框选择器未配置")
                return False
            
            self.logger.info(f"在输入框中输入消息: {message[:50]}...")
            
            # 等待输入框出现
            if not await self.wait_for_element(input_selector):
                self.logger.error("输入框未找到")
                return False
            
            # 清空输入框并输入消息
            await self.page.fill(input_selector, message)
            await asyncio.sleep(0.5)  # 等待输入完成
            
            # 3. 点击发送按钮
            send_selector = self.config.selectors.get('send_button_selector')
            if not send_selector:
                self.logger.error("发送按钮选择器未配置")
                return False
            
            self.logger.info("点击发送按钮...")
            success = await self.click_element(send_selector)
            
            if success:
                self.logger.info("消息发送成功")
                return True
            else:
                self.logger.error("点击发送按钮失败")
                return False
                
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            return False
