"""
日志管理模块
提供统一的日志配置和管理功能
"""

import logging
import logging.handlers
from pathlib import Path
from typing import Optional


def setup_logger(
    name: str = "web_interaction",
    level: str = "INFO",
    log_file: Optional[str] = None,
    max_size_mb: int = 10,
    backup_count: int = 3
) -> logging.Logger:
    """
    设置并返回配置好的日志记录器
    
    Args:
        name: 日志记录器名称
        level: 日志级别 (DEBUG, INFO, WARNING, ERROR)
        log_file: 日志文件路径，如果为None则只输出到控制台
        max_size_mb: 日志文件最大大小（MB）
        backup_count: 保留的备份文件数量
    
    Returns:
        配置好的日志记录器
    """
    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))
    
    # 清除现有的处理器（避免重复添加）
    logger.handlers.clear()
    
    # 创建格式化器 - 格式：时间-【文件名】-【级别】-【消息】
    formatter = logging.Formatter(
        '%(asctime)s-【%(filename)s】-【%(levelname)s】-【%(message)s】',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 添加控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(getattr(logging, level.upper()))
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 如果指定了日志文件，添加文件处理器
    if log_file:
        # 确保日志目录存在
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 使用RotatingFileHandler实现日志轮转
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_size_mb * 1024 * 1024,  # 转换为字节
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(getattr(logging, level.upper()))
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

        # 在日志文件开始时添加分隔线
        _add_session_separator(logger, log_file)

    return logger


def _add_session_separator(logger: logging.Logger, log_file: str):
    """
    在日志文件开始时添加会话分隔线

    Args:
        logger: 日志记录器
        log_file: 日志文件路径
    """
    import datetime

    # 检查日志文件是否已存在且不为空
    log_path = Path(log_file)
    file_exists = log_path.exists() and log_path.stat().st_size > 0

    if file_exists:
        # 如果文件已存在，添加分隔线
        separator = "=" * 80
        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        session_start = f"新会话开始于: {timestamp}"

        # 直接写入文件，避免通过日志格式化器
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f"\n{separator}\n")
            f.write(f"{session_start}\n")
            f.write(f"{separator}\n")

    # 记录会话开始日志
    logger.info("日志系统初始化完成")


def get_logger(name: str = "web_interaction") -> logging.Logger:
    """
    获取已配置的日志记录器
    
    Args:
        name: 日志记录器名称
    
    Returns:
        日志记录器实例
    """
    return logging.getLogger(name)


class LoggerMixin:
    """
    日志记录器混入类
    为其他类提供便捷的日志记录功能
    """

    @property
    def logger(self) -> logging.Logger:
        """获取当前类的日志记录器"""
        # 使用统一的日志记录器名称，确保所有日志都写入同一个文件
        return get_logger("web_interaction")
