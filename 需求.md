---

### **网页互动程序需求文档 (分阶段实施版)**

> **文档版本：v2.0 (分阶段)**  
> **核心理念**：以"简洁高效"为原则，优先构建最小可行产品（MVP），分阶段迭代，确保项目快速启动与平稳扩展。

---

### **第一阶段：核心功能 MVP (Minimum Viable Product)**

**目标：快速搭建一个可用的基础框架，验证核心交互流程。**

#### **1. 核心功能**

*   **通用框架设计**
    *   **必需**：设计一个通用框架，支持为不同网站创建独立的配置文件。
    *   **必需**：每个网站配置包含该网站特定的选择器和交互流程。
    *   **必需**：程序启动时加载指定网站的配置文件，而不是支持任意网址的即时交互。
    *   **必需**：提供网站配置模板，便于用户为新网站创建配置。

*   **极简UI界面 (PyQt6)**
    *   **必需**：地址栏、跳转按钮（仅限于跳转到配置文件中预设的URL）。
    *   **必需**：一个用于发送消息的文本输入框和一个"发送"按钮。
    *   **必需**：一个用于展示抓取消息的只读文本区域。
    *   **必需**：网站配置选择下拉菜单，用于切换不同网站的配置。
    *   **暂缓**：复杂状态显示、刷新/前进/后退按钮。

*   **网页交互引擎 (Python + Playwright)**
    *   **必需**：保存目标网页的原始HTML文件到本地，用于后续选择器设置和验证。
    *   **必需**：提供选择器设置界面，让用户基于保存的HTML文件手动设置所有必要的选择器。
    *   **必需**：用户需手动设置"消息显示区"、"消息输入框"和"发送按钮"的选择器。
    *   **必需**：支持向指定的输入框填入文本并模拟点击发送。
    *   **必需**：通过**轮询机制**（如每秒一次）定时抓取"消息显示区"的文本内容。
    *   **暂缓**：自动识别元素、AJAX/WebSocket监听、消息去重、图像抓取。

*   **登录管理**
    *   **功能目标**：确保程序可以识别网页的登录状态，并维持已登录状态，减少人工干预。
    *   **登录状态检测**：
        *   用户需手动设置以下选择器：
            *   `logged_in_indicator`：当此元素存在时，表示已登录（如用户头像、用户名显示区等）
            *   `logged_out_indicator`：当此元素存在时，表示未登录（如登录按钮等）
        *   定期检查登录状态（默认60秒检查一次，可配置）
        *   当检测到登录状态变化时（从已登录变为未登录），记录日志并通过 UI 或 API 通知
    *   **登录状态存储**：
        *   使用 Playwright 的 `storage_state()` 方法保存浏览器上下文状态，包括：
            *   Cookies
            *   localStorage
            *   sessionStorage
        *   将状态保存到本地文件（默认为 `cookies.json` 和 `session.json`）
        *   程序启动时自动尝试加载之前保存的状态
    *   **登录状态管理**：
        *   提供手动触发保存当前登录状态的功能
        *   提供手动恢复之前保存的登录状态的功能
        *   当检测到登录失效时，自动尝试使用已保存的状态恢复（可选配置）

*   **基础反爬虫机制**
    *   **必需**：集成 `playwright-stealth` 插件，隐藏浏览器特征。
    *   **必需**：支持在配置文件中设置**随机化操作延时**（如 1-3秒）。
    *   **暂缓**：User-Agent轮换库、代理IP池、鼠标轨迹模拟、验证码自动处理。

*   **API 接口 (FastAPI)**
    *   **必需**：提供简单的基础功能API端点：
        *   `POST /api/send`：接收一个JSON对象 `{"message": "..."}`，将消息发送到网页。
        *   `GET /api/messages`：返回一个包含最新抓取到的消息列表的JSON。
        *   `POST /api/navigate`：接收 `{"url": "..."}`，控制浏览器跳转到配置文件中预设的URL。
        *   `GET /api/sites`：返回所有可用的网站配置列表。
        *   `POST /api/site/select`：接收 `{"site_id": "..."}`，切换到指定的网站配置。
    *   **必需**：提供简单的登录状态相关API端点：
        *   `GET /api/login/status`：获取当前登录状态（返回 `{"logged_in": true/false}`）。
        *   `POST /api/login/save`：手动保存当前登录状态（返回 `{"success": true/false}`）。
        *   `POST /api/login/restore`：恢复之前保存的登录状态（返回 `{"success": true/false}`）。
    *   **暂缓**：发送优先级、消息分页、API认证、选择器设置API。

*   **配置管理**
    *   **必需**：提供网站配置文件目录结构：
        ```
        configs/
        ├── site1/
        │   ├── config.json
        │   ├── cookies.json
        │   └── session.json
        ├── site2/
        │   ├── config.json
        │   ├── cookies.json
        │   └── session.json
        └── ...
        ```
    *   **必需**：每个网站的 `config.json` 文件格式：
        ```json
        {
          "site_id": "site1",
          "site_name": "示例网站1",
          "target_url": "https://example.com/chat",
          "html_save_path": "saved_pages/site1.html",
          "selectors": {
            "message_box_selector": "",
            "input_box_selector": "",
            "send_button_selector": "",
            "login_status": {
              "logged_in_indicator": "",
              "logged_out_indicator": "",
              "check_interval_seconds": 60,
              "auto_restore_on_logout": true
            }
          },
          "storage": {
            "cookies_file": "cookies.json",
            "session_storage_file": "session.json",
            "auto_save_interval_minutes": 30
          },
          "anti_crawler": {
            "request_delay_ms": [1000, 3000] // [min, max]
          }
        }
        ```

#### **2. 交付物**
*   可运行的Python源代码。
*   包含上述配置的示例网站配置文件。
*   基础用户操作说明，包括如何保存HTML、设置选择器和设置登录状态检测。
*   选择器设置界面，用于基于保存的HTML文件设置和测试选择器。
*   网站配置模板和创建新网站配置的说明文档。

---

### **第二阶段：增强稳定性与反爬能力**

**目标：提升程序的健壮性、反检测能力和可用性。**

#### **1. 核心功能增强**

*   **UI界面**
    *   **新增**：状态栏，显示"连接中"、"已连接"、"检测到反爬"等基本状态。
    *   **新增**：刷新、前进、后退按钮。

*   **网页交互引擎**
    *   **优化**：消息抓取机制增加**内容去重**功能，避免重复显示。
    *   **优化**：为每条消息附加**时间戳**。

*   **登录管理增强**
    *   **新增**：支持多账号配置文件，允许快速切换不同的登录状态。
    *   **新增**：登录失效时，支持发送邮件或其他形式的通知。
    *   **新增**：提供简单的登录流程自动化脚本模板。

*   **反爬虫机制**
    *   **新增**：集成**代理IP池**，支持从 `proxies.txt` 文件加载列表并轮换。
    *   **新增**：内置一个精简的 **User-Agent 列表**（约50个），在每次会话时随机选择。
    *   **新增**：当配置文件中的 `blocked_indicators`（如"访问被拒绝"）关键词出现时，自动切换IP并刷新页面。

*   **API 接口**
    *   **优化**：`GET /api/messages` 接口支持 `limit` 参数进行分页。
    *   **优化**：`POST /api/send` 接口支持 `priority` 参数（高/低），但暂不实现复杂队列，仅为后续扩展预留。
    *   **新增**：提供选择器设置相关的API端点：
        *   `POST /api/html/save`：保存当前页面HTML（返回 `{"success": true/false, "path": "保存路径"}`）。
        *   `POST /api/selectors/set`：设置选择器（接收 `{"selector_type": "...", "value": "..."}`）。
        *   `GET /api/selectors/test`：测试选择器在保存的HTML上是否有效（返回 `{"valid": true/false, "matches": 数量}`）。

*   **安全与日志**
    *   **新增**：引入 `bleach` 库对用户输入进行基础的 **XSS 清洗**。
    *   **新增**：建立**审计日志**，记录所有API调用、页面导航和反爬事件。

#### **2. 交付物**
*   功能增强后的源代码。
*   **基础的API文档**。

---

### **第三阶段：高级功能与自动化**

**目标：引入智能化特性，减少人工干预，提升扩展性。**

#### **1. 核心功能增强**

*   **网页交互引擎**
    *   **探索**：研究使用 `MutationObserver` 注入，实现对消息区域的**实时监听**，取代轮询。
    *   **新增**：支持通过配置文件定义简单的**多步骤交互**（如点击弹窗、关闭广告）。

*   **登录管理增强**
    *   **新增**：提供完整的登录流程自动化，包括填写表单、处理验证码等。
    *   **新增**：智能识别登录表单结构，减少手动配置。
    *   **新增**：登录失效预警，根据会话时长预测可能的登录过期。

*   **反爬虫机制**
    *   **新增**：**鼠标轨迹模拟**（基于贝塞尔曲线算法）。
    *   **新增**：**验证码处理机制**：当检测到验证码时，暂停程序，并通过API/日志发出警报，等待人工处理或对接第三方OCR服务。
    *   **新增**：**熔断机制**：连续触发反爬时，自动休眠指定时间。

*   **扩展性与部署**
    *   **新增**：提供一个**网站适配模板生成工具**（命令行脚本），引导用户通过回答问题（输入选择器等）快速生成配置文件。
    *   **实现**：打包为 **Windows/Linux 单文件可执行程序**。
    *   **实现**：`--api-only` 启动模式。

#### **2. 交付物**
*   完整的可执行文件。
*   符合 OpenAPI 3.0 规范的**正式API文档**。
*   适配模板生成工具。

---

如果这份分阶段的计划符合您的预期，我将立即为您更新 `需求.md` 文件。