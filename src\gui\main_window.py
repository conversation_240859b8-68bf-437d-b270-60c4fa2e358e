"""
主窗口界面
基于PyQt6实现的极简GUI界面
"""

import asyncio
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QComboBox, QPushButton, QLineEdit, QTextEdit,
    QLabel, QStatusBar, QMessageBox, QSplitter
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont

from ..core.config_manager import ConfigManager
from ..core.logger import LoggerMixin
from ..engine.web_engine import WebEngine


class MainWindow(QMainWindow, LoggerMixin):
    """主窗口类"""
    
    # 信号定义
    message_received = pyqtSignal(str)
    status_changed = pyqtSignal(str)
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化主窗口
        
        Args:
            config_manager: 配置管理器实例
        """
        super().__init__()
        
        self.config_manager = config_manager
        self.web_engine = WebEngine()
        self.close_requested = False  # 添加关闭请求标志
        self.last_login_prompt_time = 0  # 上次显示登录提示的时间

        # 浏览器状态管理
        self.browser_connected = False
        
        # 初始化UI
        self._init_ui()
        
        # 连接信号
        self._connect_signals()
        
        # 启动定时器更新消息显示
        self._setup_timers()
        
        # 延迟异步初始化
        QTimer.singleShot(100, self._delayed_init)
    
    def _init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("网页互动程序 - MVP版本")
        self.setGeometry(100, 100, 1024, 768)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 顶部控制区域
        self._create_control_panel(main_layout)
        
        # 中间内容区域
        self._create_content_area(main_layout)
        
        # 底部输入区域
        self._create_input_area(main_layout)
        
        # 状态栏
        self._create_status_bar()
    
    def _create_control_panel(self, parent_layout):
        """创建顶部控制面板"""
        control_layout = QHBoxLayout()
        
        # 网站选择下拉框
        self.site_combo = QComboBox()
        self.site_combo.setMinimumWidth(200)
        control_layout.addWidget(QLabel("选择网站:"))
        control_layout.addWidget(self.site_combo)
        
        # 导航按钮
        self.navigate_btn = QPushButton("打开网页")
        self.navigate_btn.setMinimumWidth(100)
        control_layout.addWidget(self.navigate_btn)

        # 关闭浏览器按钮
        self.close_browser_btn = QPushButton("关闭浏览器")
        self.close_browser_btn.setMinimumWidth(100)
        self.close_browser_btn.setEnabled(False)  # 初始状态禁用
        control_layout.addWidget(self.close_browser_btn)

        # 地址栏
        self.url_edit = QLineEdit()
        self.url_edit.setReadOnly(True)
        self.url_edit.setPlaceholderText("当前网站URL将显示在这里")
        control_layout.addWidget(QLabel("地址:"))
        control_layout.addWidget(self.url_edit)
        
        parent_layout.addLayout(control_layout)
    
    def _create_content_area(self, parent_layout):
        """创建中间内容区域"""
        # 使用分割器创建可调整大小的区域
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 消息显示区域
        self.message_display = QTextEdit()
        self.message_display.setReadOnly(True)
        self.message_display.setPlaceholderText("抓取到的消息将显示在这里...")
        
        # 设置字体
        font = QFont("Consolas", 10)
        self.message_display.setFont(font)
        
        splitter.addWidget(self.message_display)
        
        # 设置分割器比例
        splitter.setSizes([800, 200])
        
        parent_layout.addWidget(splitter)
    
    def _create_input_area(self, parent_layout):
        """创建底部输入区域"""
        input_layout = QHBoxLayout()
        
        # 消息输入框 - 增大高度
        self.message_input = QLineEdit()
        self.message_input.setPlaceholderText("在此输入要发送的消息...")
        self.message_input.setMinimumHeight(40)  # 增大高度
        self.message_input.setFont(QFont("Arial", 12))  # 增大字体
        self.message_input.returnPressed.connect(self._send_message)
        input_layout.addWidget(self.message_input)
        
        # 发送按钮
        self.send_btn = QPushButton("发送")
        self.send_btn.setMinimumHeight(40)  # 调整按钮高度
        self.send_btn.setMaximumWidth(100)  # 增大宽度
        self.send_btn.setFont(QFont("Arial", 12))  # 增大字体
        input_layout.addWidget(self.send_btn)
        
        parent_layout.addLayout(input_layout)
    
    def _create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
        
        # 登录状态标签
        self.login_status_label = QLabel("登录状态: 未知")
        self.status_bar.addPermanentWidget(self.login_status_label)
    
    def _connect_signals(self):
        """连接信号和槽"""
        # 按钮点击事件
        self.navigate_btn.clicked.connect(self._navigate_to_site)
        self.close_browser_btn.clicked.connect(self._close_browser)
        self.send_btn.clicked.connect(self._send_message)

        # 下拉框选择事件 - 选择网站后更新配置
        self.site_combo.currentTextChanged.connect(self._on_site_changed)

        # 自定义信号
        self.message_received.connect(self._on_message_received)
        self.status_changed.connect(self._on_status_changed)
    
    def _setup_timers(self):
        """设置定时器"""
        # 消息更新定时器
        self.message_timer = QTimer()
        self.message_timer.timeout.connect(self._update_messages)
        self.message_timer.start(1000)  # 每秒更新一次
        
        # 登录状态检查定时器
        self.login_timer = QTimer()
        self.login_timer.timeout.connect(self._check_login_status)
        self.login_timer.start(5000)  # 每5秒检查一次
    
    def _delayed_init(self):
        """延迟初始化"""
        asyncio.create_task(self._async_init())

    async def _async_init(self):
        """异步初始化"""
        try:
            # 启动网页引擎
            await self.web_engine.start(headless=False)

            # 加载网站列表
            self._load_site_list()

            self.status_changed.emit("网页引擎已启动")

        except Exception as e:
            self.logger.error(f"异步初始化失败: {e}")
            self.status_changed.emit(f"初始化失败: {e}")
    
    def _load_site_list(self):
        """加载网站列表到下拉框"""
        # 临时断开信号连接
        self.site_combo.currentTextChanged.disconnect(self._on_site_changed)

        self.site_combo.clear()

        sites = self.config_manager.get_site_list()
        for site in sites:
            self.site_combo.addItem(site['site_name'], site['site_id'])

        if sites:
            # 默认选择第一个网站（不会触发信号）
            first_site_id = sites[0]['site_id']
            self.config_manager.set_current_site(first_site_id)
            self._update_url_display()

        # 重新连接信号
        self.site_combo.currentTextChanged.connect(self._on_site_changed)
    
    def _on_site_changed(self, site_name: str):
        """网站选择改变事件 - 仅更新配置，不自动跳转"""
        if not site_name:
            return

        # 获取选中的网站ID
        current_index = self.site_combo.currentIndex()
        if current_index >= 0:
            site_id = self.site_combo.itemData(current_index)
            self.config_manager.set_current_site(site_id)
            self._update_url_display()

            # 不再自动跳转到选择的网站
            # asyncio.create_task(self._async_navigate())
            self.status_changed.emit(f"已选择网站: {site_name}，点击导航按钮开始连接")
    
    def _update_url_display(self):
        """更新地址栏显示"""
        config = self.config_manager.get_current_site_config()
        if config:
            self.url_edit.setText(config.target_url)
    
    def _navigate_to_site(self):
        """导航到选中的网站"""
        asyncio.create_task(self._async_navigate())
    
    async def _async_navigate(self):
        """异步导航"""
        try:
            # 检查是否已经连接到浏览器
            if self.browser_connected:
                self.status_changed.emit("浏览器已连接，请先关闭当前连接")
                return

            config = self.config_manager.get_current_site_config()
            if not config:
                self.status_changed.emit("未选择网站配置")
                return

            self.status_changed.emit("正在加载网站...")

            # 禁用导航按钮，启用关闭按钮
            self.navigate_btn.setEnabled(False)
            self.close_browser_btn.setEnabled(True)

            # 加载网站配置
            site_id = self.site_combo.currentData()
            await self.web_engine.load_site_config(config, site_id)

            # 导航到网站
            success = await self.web_engine.navigate_to_site()

            if success:
                self.browser_connected = True
                self.status_changed.emit(f"已连接到: {config.site_name}")

                # 自动保存HTML
                await self._auto_save_html()
            else:
                self.status_changed.emit("导航失败")
                # 恢复按钮状态
                self._reset_button_states()

        except Exception as e:
            self.logger.error(f"导航失败: {e}")
            self.status_changed.emit(f"导航失败: {e}")
            # 恢复按钮状态
            self._reset_button_states()
            
    async def _auto_save_html(self):
        """自动保存HTML并管理保存的文件数量"""
        try:
            # 保存当前页面HTML
            file_path = await self.web_engine.save_page_html()
            
            if file_path:
                self.status_changed.emit(f"HTML已自动保存: {file_path}")
                
                # 限制保存的HTML文件数量
                await self._manage_saved_html_files()
            else:
                self.status_changed.emit("自动保存HTML失败")
                
        except Exception as e:
            self.logger.error(f"自动保存HTML失败: {e}")
            self.status_changed.emit(f"自动保存失败: {e}")
            
    async def _manage_saved_html_files(self):
        """管理保存的HTML文件，保留最新的10个文件"""
        try:
            import os
            from pathlib import Path
            
            # 获取保存目录
            save_dir = Path("saved_pages")
            if not save_dir.exists():
                return
                
            # 获取所有HTML文件并按修改时间排序
            html_files = list(save_dir.glob("*.html"))
            html_files.sort(key=lambda x: os.path.getmtime(x))
            
            # 如果文件数量超过10个，删除最旧的文件
            while len(html_files) > 10:
                oldest_file = html_files.pop(0)  # 移除并获取最旧的文件
                try:
                    oldest_file.unlink()  # 删除文件
                    self.logger.info(f"删除旧HTML文件: {oldest_file}")
                except Exception as e:
                    self.logger.error(f"删除旧HTML文件失败: {e}")
                    
        except Exception as e:
            self.logger.error(f"管理HTML文件失败: {e}")

    def _close_browser(self):
        """关闭浏览器连接"""
        asyncio.create_task(self._async_close_browser())

    async def _async_close_browser(self):
        """异步关闭浏览器"""
        try:
            if not self.browser_connected:
                self.status_changed.emit("浏览器未连接")
                return

            self.status_changed.emit("正在关闭浏览器...")

            # 停止网页引擎
            await self.web_engine.stop()

            # 重新启动引擎（为下次连接做准备）
            await self.web_engine.start(headless=False)

            # 更新状态
            self.browser_connected = False
            self._reset_button_states()

            # 清空消息显示
            self.message_display.clear()

            self.status_changed.emit("浏览器已关闭")

        except Exception as e:
            self.logger.error(f"关闭浏览器失败: {e}")
            self.status_changed.emit(f"关闭失败: {e}")

    def _reset_button_states(self):
        """重置按钮状态"""
        self.navigate_btn.setEnabled(True)
        self.close_browser_btn.setEnabled(False)

    def _send_message(self):
        """发送消息"""
        if not self.browser_connected:
            self.status_changed.emit("请先连接到网站")
            return

        message = self.message_input.text().strip()
        if not message:
            return

        asyncio.create_task(self._async_send_message(message))
    
    async def _async_send_message(self, message: str):
        """异步发送消息"""
        try:
            self.status_changed.emit("正在发送消息...")

            # 使用新的发送并获取回复方法
            reply = await self.web_engine.send_message_and_get_response(message)

            if reply and not reply.startswith("操作失败"):
                self.message_input.clear()
                self.status_changed.emit("消息发送成功，已获取回复")

                # 在消息显示区域显示对话
                current_text = self.message_display.toPlainText()
                new_text = f"{current_text}\n\n【用户】: {message}\n\n【回复】: {reply}\n"
                self.message_display.setPlainText(new_text)

                # 滚动到底部
                cursor = self.message_display.textCursor()
                cursor.movePosition(cursor.MoveOperation.End)
                self.message_display.setTextCursor(cursor)
            else:
                self.status_changed.emit(f"发送失败: {reply}")

        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            self.status_changed.emit(f"发送失败: {e}")
    

    def _check_login_status(self):
        """检查登录状态"""
        asyncio.create_task(self._async_check_login_status())
        
    async def _async_check_login_status(self):
        """异步检查登录状态"""
        try:
            login_status = await self.web_engine._check_login_status()

            if login_status is True:
                self.login_status_label.setText("登录状态: 已登录")
            elif login_status is False:
                self.login_status_label.setText("登录状态: 未登录")
                
                # 获取当前配置
                current_config = self.web_engine.current_config
                if current_config and self.web_engine.page:
                    # 检查是否需要显示提示（每60秒最多显示一次）
                    current_time = asyncio.get_event_loop().time()
                    if current_time - self.last_login_prompt_time > 60:
                        # 从配置中获取登出选择器
                        login_config = current_config.selectors.get('login_status', {})
                        logged_out_selector = login_config.get('logged_out_indicator')
                        
                        if logged_out_selector:
                            # 检查选择器是否存在
                            element = await self.web_engine.page.query_selector(logged_out_selector)
                            if element:
                                self.last_login_prompt_time = current_time
                                
                                # 显示登录提示对话框
                                from PyQt5.QtCore import QTimer
                                
                                def show_login_dialog():
                                    QMessageBox.warning(
                                        self, 
                                        "需要登录", 
                                        "检测到您尚未登录，请先登录后再继续操作。",
                                        QMessageBox.Ok
                                    )
                                
                                # 使用QTimer在主线程中显示对话框
                                QTimer.singleShot(0, show_login_dialog)
            else:
                self.login_status_label.setText("登录状态: 未知")

        except Exception as e:
            self.logger.debug(f"检查登录状态失败: {e}")
    
    def _update_messages(self):
        """更新消息显示"""
        asyncio.create_task(self._async_update_messages())
    
    async def _async_update_messages(self):
        """异步更新消息"""
        try:
            messages = await self.web_engine.get_messages()
            
            # 只显示最新的50条消息
            recent_messages = messages[-50:] if len(messages) > 50 else messages
            
            # 格式化消息文本
            message_text = ""
            for msg in recent_messages:
                timestamp = msg.get('timestamp', 0)
                text = msg.get('text', '')
                message_text += f"[{timestamp:.0f}] {text}\n"
            
            # 更新显示
            if message_text != self.message_display.toPlainText():
                self.message_display.setPlainText(message_text)
                
                # 滚动到底部
                cursor = self.message_display.textCursor()
                cursor.movePosition(cursor.MoveOperation.End)
                self.message_display.setTextCursor(cursor)
                
        except Exception as e:
            self.logger.debug(f"更新消息显示失败: {e}")
    
    def _on_message_received(self, message: str):
        """处理接收到的消息信号"""
        # 这个方法由信号触发，用于线程安全的UI更新
        pass
    
    def _on_status_changed(self, status: str):
        """处理状态改变信号"""
        self.status_label.setText(status)
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 在运行中的事件循环中执行异步操作
        self.logger.info("正在关闭窗口，等待浏览器引擎停止...")
        
        # 使用信号/槽机制来安全地关闭窗口
        self.close_requested = True
        
        # 创建一个异步任务来停止引擎，并在完成后退出应用
        stop_task = asyncio.create_task(self.web_engine.stop())
        
        # 添加回调函数，在引擎停止后执行清理操作
        def on_engine_stopped(task):
            try:
                # 获取任务结果（如果有异常会抛出）
                task.result()
                self.logger.info("浏览器引擎已完全停止，窗口将关闭")
            except Exception as e:
                self.logger.error(f"停止浏览器引擎时出错: {e}")
            finally:
                # 关闭所有定时器
                self.message_timer.stop()
                self.login_timer.stop()
                
                # 退出事件循环
                loop = asyncio.get_event_loop()
                loop.call_soon(loop.stop)
        
        # 添加回调
        stop_task.add_done_callback(on_engine_stopped)
        
        # 接受关闭事件，但实际关闭会在回调中处理
        event.accept()
