#!/usr/bin/env python3
"""
网页互动程序 - 主入口
"""

import sys
import asyncio
from pathlib import Path

# 确保项目根目录在Python路径中
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    import qasync
    from PyQt5.QtWidgets import QApplication
except ImportError as e:
    print(f"❌ 缺少关键依赖: {e}")
    print("请安装: conda install pyqt && pip install qasync")
    sys.exit(1)

from src.core.config_manager import ConfigManager
from src.core.logger import setup_logger
from src.gui.main_window import MainWindow


def main():
    """程序入口点"""
    # 设置日志
    logger = setup_logger(
        name="web_interaction",
        level="INFO",
        log_file="logs/web_interaction.log"
    )

    # 初始化配置管理器
    config_manager = ConfigManager("configs")

    # 创建QApplication
    app = QApplication(sys.argv)
    app.setQuitOnLastWindowClosed(False)  # 防止窗口关闭时立即退出应用

    # 设置qasync事件循环
    loop = qasync.QEventLoop(app)
    asyncio.set_event_loop(loop)

    # 创建主窗口
    main_window = MainWindow(config_manager)
    main_window.show()
    
    # 添加应用退出处理
    def clean_exit():
        logger.info("应用程序即将退出")
        app.quit()
    
    app.aboutToQuit.connect(clean_exit)

    # 运行事件循环
    with loop:
        try:
            loop.run_forever()
        except KeyboardInterrupt:
            logger.info("程序被用户中断")
        finally:
            # 确保所有剩余任务都被取消
            pending = asyncio.all_tasks(loop)
            for task in pending:
                task.cancel()
            
            # 等待所有任务完成取消
            if pending:
                logger.info(f"正在等待 {len(pending)} 个任务完成取消...")
                try:
                    loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
                except RuntimeError:
                    # 如果事件循环已关闭，则忽略错误
                    pass
            
            logger.info("程序退出")


if __name__ == "__main__":
    main()
