"""
通用配置管理模块
负责加载、验证和管理网站配置文件的通用框架
支持任意网站的配置管理，不绑定特定网站功能
"""

import json
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict

from .logger import LoggerMixin


@dataclass
class SiteConfig:
    """网站配置数据类 - 通用配置结构"""
    site_id: str
    site_name: str
    target_url: str
    html_save_path: str
    selectors: Dict[str, Any]
    storage: Dict[str, Any]
    anti_crawler: Dict[str, Any]
    site_specific: Optional[Dict[str, Any]] = None

    def get_selector(self, selector_path: str, default: Any = None) -> Any:
        """
        获取选择器配置，支持嵌套路径

        Args:
            selector_path: 选择器路径，如 'login_status.logged_in_indicator'
            default: 默认值

        Returns:
            选择器配置值
        """
        keys = selector_path.split('.')
        value = self.selectors

        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default

        return value

    def get_storage_config(self, key: str, default: Any = None) -> Any:
        """获取存储配置"""
        return self.storage.get(key, default)

    def get_anti_crawler_config(self, key: str, default: Any = None) -> Any:
        """获取反爬虫配置"""
        return self.anti_crawler.get(key, default)


class ConfigManager(LoggerMixin):
    """
    通用配置管理器
    提供网站配置的加载、验证、管理等通用功能
    不包含特定网站的业务逻辑
    """

    def __init__(self, config_dir: str = "configs"):
        """
        初始化配置管理器

        Args:
            config_dir: 配置文件目录路径
        """
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)

        self._sites: Dict[str, SiteConfig] = {}
        self._current_site_id: Optional[str] = None

        # 加载所有网站配置
        self._load_all_configs()

        self.logger.info(f"配置管理器初始化完成，加载了 {len(self._sites)} 个网站配置")
    
    def _load_all_configs(self):
        """扫描并加载所有网站配置"""
        if not self.config_dir.exists():
            self.logger.warning(f"配置目录不存在: {self.config_dir}")
            return

        # 扫描配置目录下的所有子目录
        for site_dir in self.config_dir.iterdir():
            if site_dir.is_dir():
                config_file = site_dir / "config.json"
                if config_file.exists():
                    try:
                        self._load_site_config(site_dir.name, config_file)
                    except Exception as e:
                        self.logger.error(f"加载网站配置失败 {site_dir.name}: {e}")

    def _load_site_config(self, site_id: str, config_file: Path):
        """
        加载单个网站配置文件

        Args:
            site_id: 网站标识符
            config_file: 配置文件路径
        """
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
        except (json.JSONDecodeError, FileNotFoundError) as e:
            raise ValueError(f"配置文件格式错误或不存在: {e}")

        # 验证配置结构
        self._validate_config_structure(config_data, site_id)

        # 应用默认配置
        config_data = self._apply_default_config(config_data, site_id)

        # 创建配置对象
        site_config = SiteConfig(**config_data)
        self._sites[site_id] = site_config

        self.logger.info(f"成功加载网站配置: {site_config.site_name} ({site_id})")

    def _validate_config_structure(self, config_data: Dict[str, Any], site_id: str):
        """
        验证配置文件结构

        Args:
            config_data: 配置数据
            site_id: 网站ID
        """
        required_fields = ['site_name', 'target_url', 'selectors']
        for field in required_fields:
            if field not in config_data:
                raise ValueError(f"网站 {site_id} 配置文件缺少必需字段: {field}")

        # 验证 selectors 结构
        if not isinstance(config_data['selectors'], dict):
            raise ValueError(f"网站 {site_id} 的 selectors 字段必须是字典类型")

    def _apply_default_config(self, config_data: Dict[str, Any], site_id: str) -> Dict[str, Any]:
        """
        应用默认配置值

        Args:
            config_data: 原始配置数据
            site_id: 网站ID

        Returns:
            应用默认值后的配置数据
        """
        # 设置基础默认值
        config_data.setdefault('site_id', site_id)
        config_data.setdefault('html_save_path', f"saved_pages/{site_id}.html")

        # 设置存储相关默认值
        config_data.setdefault('storage', {})
        storage_defaults = {
            'cookies_file': f'{site_id}_cookies.json',
            'session_storage_file': f'{site_id}_session.json',
            'auto_save_interval_minutes': 30
        }
        for key, value in storage_defaults.items():
            config_data['storage'].setdefault(key, value)

        # 设置反爬虫默认值
        config_data.setdefault('anti_crawler', {})
        anti_crawler_defaults = {
            'request_delay_ms': [1000, 3000],
            'user_agent_rotation': False,
            'proxy_rotation': False
        }
        for key, value in anti_crawler_defaults.items():
            config_data['anti_crawler'].setdefault(key, value)

        # 设置网站特定配置默认值（可选）
        config_data.setdefault('site_specific', None)

        return config_data
    
    def get_site_list(self) -> List[Dict[str, str]]:
        """
        获取所有网站配置列表
        
        Returns:
            网站配置列表，包含site_id和site_name
        """
        return [
            {"site_id": site_id, "site_name": config.site_name}
            for site_id, config in self._sites.items()
        ]
    
    def get_site_config(self, site_id: str) -> Optional[SiteConfig]:
        """
        获取指定网站的配置
        
        Args:
            site_id: 网站ID
        
        Returns:
            网站配置对象，如果不存在则返回None
        """
        return self._sites.get(site_id)
    
    def get_current_site_config(self) -> Optional[SiteConfig]:
        """
        获取当前选中的网站配置
        
        Returns:
            当前网站配置对象，如果未选中则返回None
        """
        if self._current_site_id:
            return self.get_site_config(self._current_site_id)
        return None
    
    def set_current_site(self, site_id: str) -> bool:
        """
        设置当前选中的网站
        
        Args:
            site_id: 网站ID
        
        Returns:
            设置是否成功
        """
        if site_id in self._sites:
            self._current_site_id = site_id
            self.logger.info(f"切换到网站: {self._sites[site_id].site_name}")
            return True
        else:
            self.logger.error(f"网站配置不存在: {site_id}")
            return False
    
    def get_current_site_id(self) -> Optional[str]:
        """获取当前选中的网站ID"""
        return self._current_site_id
    
    def create_site_config_template(self, site_id: str, site_name: str, target_url: str,
                                   custom_selectors: Optional[Dict[str, Any]] = None) -> bool:
        """
        创建新的网站配置模板

        Args:
            site_id: 网站标识符
            site_name: 网站显示名称
            target_url: 目标网站URL
            custom_selectors: 自定义选择器配置（可选）

        Returns:
            创建是否成功
        """
        try:
            # 检查是否已存在
            if site_id in self._sites:
                self.logger.warning(f"网站配置已存在: {site_id}")
                return False

            # 创建网站配置目录
            site_dir = self.config_dir / site_id
            site_dir.mkdir(exist_ok=True)

            # 创建基础配置模板
            config_template = {
                "site_id": site_id,
                "site_name": site_name,
                "target_url": target_url,
                "html_save_path": f"saved_pages/{site_id}.html",
                "selectors": custom_selectors or self._get_default_selectors(),
                "storage": {
                    "cookies_file": f"{site_id}_cookies.json",
                    "session_storage_file": f"{site_id}_session.json",
                    "auto_save_interval_minutes": 30
                },
                "anti_crawler": {
                    "request_delay_ms": [1000, 3000],
                    "user_agent_rotation": False,
                    "proxy_rotation": False
                }
            }

            # 保存配置文件
            config_file = site_dir / "config.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_template, f, indent=2, ensure_ascii=False)

            # 重新加载配置
            self._load_site_config(site_id, config_file)

            self.logger.info(f"成功创建网站配置模板: {site_name}")
            return True

        except Exception as e:
            self.logger.error(f"创建网站配置模板失败: {e}")
            return False

    def _get_default_selectors(self) -> Dict[str, Any]:
        """
        获取默认选择器模板

        Returns:
            默认选择器配置
        """
        return {
            "message_box_selector": "",
            "input_box_selector": "",
            "send_button_selector": "",
            "login_status": {
                "logged_in_indicator": "",
                "logged_out_indicator": "",
                "check_interval_seconds": 60,
                "auto_restore_on_logout": True
            }
        }
    
    def save_site_config(self, site_id: str, config: SiteConfig) -> bool:
        """
        保存网站配置到文件

        Args:
            site_id: 网站标识符
            config: 网站配置对象

        Returns:
            保存是否成功
        """
        try:
            site_dir = self.config_dir / site_id
            site_dir.mkdir(exist_ok=True)

            config_file = site_dir / "config.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(config), f, indent=2, ensure_ascii=False)

            # 更新内存中的配置
            self._sites[site_id] = config

            self.logger.info(f"成功保存网站配置: {config.site_name}")
            return True

        except Exception as e:
            self.logger.error(f"保存网站配置失败: {e}")
            return False

    def delete_site_config(self, site_id: str) -> bool:
        """
        删除网站配置

        Args:
            site_id: 网站标识符

        Returns:
            删除是否成功
        """
        try:
            if site_id not in self._sites:
                self.logger.warning(f"网站配置不存在: {site_id}")
                return False

            # 删除配置文件
            site_dir = self.config_dir / site_id
            if site_dir.exists():
                config_file = site_dir / "config.json"
                if config_file.exists():
                    config_file.unlink()

                # 如果目录为空，删除目录
                if not any(site_dir.iterdir()):
                    site_dir.rmdir()

            # 从内存中移除
            site_name = self._sites[site_id].site_name
            del self._sites[site_id]

            # 如果删除的是当前选中的网站，清除选中状态
            if self._current_site_id == site_id:
                self._current_site_id = None

            self.logger.info(f"成功删除网站配置: {site_name}")
            return True

        except Exception as e:
            self.logger.error(f"删除网站配置失败: {e}")
            return False

    def reload_config(self, site_id: Optional[str] = None) -> bool:
        """
        重新加载配置

        Args:
            site_id: 指定网站ID，如果为None则重新加载所有配置

        Returns:
            重新加载是否成功
        """
        try:
            if site_id:
                # 重新加载指定网站配置
                config_file = self.config_dir / site_id / "config.json"
                if config_file.exists():
                    self._load_site_config(site_id, config_file)
                    self.logger.info(f"成功重新加载网站配置: {site_id}")
                else:
                    self.logger.error(f"配置文件不存在: {config_file}")
                    return False
            else:
                # 重新加载所有配置
                self._sites.clear()
                self._current_site_id = None
                self._load_all_configs()
                self.logger.info("成功重新加载所有网站配置")

            return True

        except Exception as e:
            self.logger.error(f"重新加载配置失败: {e}")
            return False

    def validate_site_config(self, site_id: str) -> bool:
        """
        验证网站配置的完整性

        Args:
            site_id: 网站标识符

        Returns:
            配置是否有效
        """
        config = self.get_site_config(site_id)
        if not config:
            return False

        try:
            # 验证基本字段
            if not all([config.site_name, config.target_url, config.selectors]):
                return False

            # 验证URL格式
            if not config.target_url.startswith(('http://', 'https://')):
                return False

            # 验证选择器结构
            if not isinstance(config.selectors, dict):
                return False

            self.logger.info(f"网站配置验证通过: {site_id}")
            return True

        except Exception as e:
            self.logger.error(f"网站配置验证失败 {site_id}: {e}")
            return False
